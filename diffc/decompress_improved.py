#!/usr/bin/env python3
"""
Improved decompression script that reads actual DKL values from the compressed file
"""

import argparse
from pathlib import Path
import yaml
import zlib
import struct
import json
from easydict import EasyDict as edict

from lib import image_utils
from lib.diffc.decode import decode
from lib.diffc.denoise import denoise
from lib.diffc.rcc.gaussian_channel_simulator import GaussianChannelSimulator

def parse_args():
    parser = argparse.ArgumentParser(
        description="Decompress DiffC-compressed images using improved format with stored DKL values"
    )
    parser.add_argument(
        "--config",
        help="Path to the compression config file",
        required=True
    )
    parser.add_argument(
        "--input_path",
        default=None,
        help="Path to a single .diffc file to decompress"
    )
    parser.add_argument(
        "--input_dir",
        default=None,
        help="Path to a directory containing .diffc files to decompress"
    )
    parser.add_argument(
        "--output_dir",
        required=True,
        help="Directory to output the decompressed images to"
    )
    return parser.parse_args()

def get_noise_prediction_model(model_name, config):
    if model_name == "SD1.5":
        from lib.models.SD15 import SD15Model
        return SD15Model()
    elif model_name == "SD2.1":
        from lib.models.SD21 import SD21Model
        return SD21Model()
    elif model_name == "SDXL":
        from lib.models.SDXL import SDXLModel
        use_refiner = config.get("use_refiner", False)
        return SDXLModel(use_refiner=use_refiner)
    elif model_name == 'Flux':
        from lib.models.Flux import FluxModel
        return FluxModel()
    else:
        raise ValueError(f"Unrecognized model: {model_name}")

def read_improved_diffc_file(file_path):
    """Read compressed data from improved .diffc file format with DKL values"""
    with open(file_path, 'rb') as f:
        # Check for magic number
        magic = f.read(4)
        if magic == b'DIFC':
            # Improved format with DKL values
            caption_length = struct.unpack('<I', f.read(4))[0]
            dkl_length = struct.unpack('<I', f.read(4))[0]
            width = struct.unpack('<H', f.read(2))[0]
            height = struct.unpack('<H', f.read(2))[0]
            step_idx = struct.unpack('<H', f.read(2))[0]
            
            # Read and decompress caption
            compressed_caption = f.read(caption_length)
            caption = zlib.decompress(compressed_caption).decode('utf-8')
            
            # Read and decompress DKL values
            compressed_dkl = f.read(dkl_length)
            dkl_json = zlib.decompress(compressed_dkl).decode('utf-8')
            dkl_values = json.loads(dkl_json)
            
            # Read remaining bytes for image data
            image_bytes = list(f.read())
            
            return caption, width, height, step_idx, image_bytes, dkl_values
        else:
            # Legacy format - fallback to original method
            f.seek(0)  # Reset file pointer
            caption_length = struct.unpack('<I', f.read(4))[0]
            width = struct.unpack('<H', f.read(2))[0]
            height = struct.unpack('<H', f.read(2))[0]
            step_idx = struct.unpack('<H', f.read(2))[0]
            
            compressed_caption = f.read(caption_length)
            caption = zlib.decompress(compressed_caption).decode('utf-8')
            
            image_bytes = list(f.read())
            
            return caption, width, height, step_idx, image_bytes, None

def decompress_file(input_path, output_path, noise_prediction_model, 
                   gaussian_channel_simulator, config):
    # Read compressed data
    result = read_improved_diffc_file(input_path)
    
    if len(result) == 6:
        # Improved format with DKL values
        caption, width, height, step_idx, compressed_bytes, dkl_values = result
        print(f"✅ Using stored DKL values from compressed file")
        print(f"   DKL values: {dkl_values[:5]}..." if len(dkl_values) > 5 else f"   DKL values: {dkl_values}")
    else:
        # Legacy format - need manual DKL values
        caption, width, height, step_idx, compressed_bytes = result
        if config.manual_dkl_per_step is None:
            raise ValueError("Legacy format file requires manual_dkl_per_step in config file")
        dkl_values = config.manual_dkl_per_step[:step_idx+1]
        print(f"⚠️  Using manual DKL values from config (legacy format)")
    
    # Decompress the representation
    chunk_seeds_per_step = gaussian_channel_simulator.decompress_chunk_seeds(
        compressed_bytes, dkl_values
    )

    timestep = config.encoding_timesteps[step_idx]
    
    # Configure model with caption
    noise_prediction_model.configure(
        caption, 
        config.denoising_guidance_scale,
        width,
        height
    )
    
    # Get the noisy reconstruction    
    noisy_recon = decode(
        width,
        height,
        config.encoding_timesteps,
        noise_prediction_model,
        gaussian_channel_simulator,
        chunk_seeds_per_step,
        dkl_values,  # Use actual DKL values
        seed=0)
    
    # Denoise
    recon_latent = denoise(
        noisy_recon,
        timestep,
        config.denoising_timesteps,
        noise_prediction_model
    )
    
    # Convert to image
    recon_img_pt = noise_prediction_model.latent_to_image(recon_latent)
    recon_image = image_utils.torch_to_pil_img(recon_img_pt)
    
    # Save decompressed image
    recon_image.save(output_path)

def main():
    args = parse_args()
    
    # Load config
    with open(args.config, "r") as f:
        config = edict(yaml.safe_load(f))
    
    # Note: We don't require manual_dkl_per_step for improved format
    # but we keep it for backward compatibility with legacy files
    
    # Set up output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)

    # Get input paths
    if not bool(args.input_path) ^ bool(args.input_dir):
        raise ValueError("Must specify exactly one of --input_path or --input_dir")

    input_paths = []
    if args.input_path:
        input_paths.append(Path(args.input_path))
    else:
        input_dir = Path(args.input_dir)
        input_paths = list(input_dir.glob("*.diffc"))

    # Initialize models
    gaussian_channel_simulator = GaussianChannelSimulator(
        config.max_chunk_size, config.chunk_padding
    )
    noise_prediction_model = get_noise_prediction_model(config.model, config)

    # Process each file
    for input_path in input_paths:
        print(f"🔄 Decompressing {input_path.name}...")
        
        # Set up output path
        output_path = output_dir / f"decompressed_{input_path.stem}.png"
        
        # Decompress file
        try:
            decompress_file(
                input_path, 
                output_path, 
                noise_prediction_model, 
                gaussian_channel_simulator, 
                config
            )
            print(f"   💾 Saved: {output_path}")
        except Exception as e:
            print(f"   ❌ Failed to decompress {input_path.name}: {e}")

    print(f"\n🎉 Decompression completed! Files saved to {output_dir}")

if __name__ == "__main__":
    main()
