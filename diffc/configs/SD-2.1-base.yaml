max_chunk_size: 16
chunk_padding: 2
model: 'SD2.1'
encoding_guidance_scale: 0
denoising_guidance_scale: 0
encoding_timesteps: [972, 949, 929, 897, 869, 834, 805, 780, 751, 726, 704, 688, 670, 648, 627, 608, 591, 578, 561, 546, 530, 520, 510, 498, 491, 480, 465, 455, 447, 438, 429, 419, 410, 402, 390, 380, 371, 361, 353, 345, 336, 326, 319, 313, 305, 296, 289, 282, 276, 269, 261, 254, 247, 242, 237, 231, 224, 219, 213, 209, 204, 200, 194, 189, 185, 181, 175, 170, 167, 163, 160, 156, 153, 149, 146, 143, 139, 135, 132, 129, 125, 121, 118, 116, 113, 110, 107, 104, 101, 99, 96, 94, 92, 90, 87, 85, 82, 80, 78, 76, 74, 72, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
# manual_dkl_per_step: null
manual_dkl_per_step: [21, 20, 20, 40, 43, 66, 67, 67, 92, 92, 92, 74, 91, 123, 130, 128, 125, 102, 143, 135, 155, 102, 107, 134, 81, 133, 193, 135, 112, 130, 136, 157, 147, 135, 213, 186, 174, 202, 168, 174, 205, 237, 172, 152, 210, 247, 198, 206, 181, 219, 261, 237, 246, 181, 186, 231, 281, 207, 256, 175, 225, 186, 289, 250, 205, 211, 329, 285, 175, 241, 185, 254, 195, 268, 206, 212, 292, 302, 233, 239, 332, 345, 267, 182, 281, 290, 300, 311, 322, 220, 342, 234, 240, 246, 384, 263, 411, 284, 292, 301, 311, 321, 332, 344, 356, 370, 384, 399, 416, 433, 452, 473, 495, 520, 268, 275, 282, 290, 298, 306, 315, 325, 334, 345, 356, 368, 381, 394, 408, 424, 440, 457, 476, 496, 518, 542, 567, 596, 626, 660, 698, 740, 786, 838, 895, 962, 1039, 1125, 1228, 1345, 1488, 1663, 1877, 2148, 2506, 2992, 3674, 4715, 6488, 10135, 21339]
recon_timesteps: [900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10]
denoising_timesteps: [981, 961, 941, 921, 901, 881, 861, 841, 821, 801, 781, 761, 741, 721, 701, 681, 661, 641, 621, 601, 581, 561, 541, 521, 501, 481, 461, 441, 421, 401, 381, 361, 341, 321, 301, 281, 261, 241, 221, 201, 181, 161, 141, 121, 101, 81, 61, 41, 21, 10, 5, 0] 
