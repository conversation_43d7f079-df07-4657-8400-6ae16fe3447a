# Low BPP configuration - High compression, lower quality
max_chunk_size: 16
chunk_padding: 2
model: 'SD1.5'
encoding_guidance_scale: 0
denoising_guidance_scale: 0
encoding_timesteps: [972, 949, 929, 897, 869, 834, 805, 780, 751, 726, 704, 688, 670, 648, 627, 608, 591, 578, 561, 546, 530, 520, 510, 498, 491, 480, 465, 455, 447, 438, 429, 419, 410, 402, 390, 380, 371, 361, 353, 345, 336, 326, 319, 313, 305, 296, 289, 282, 276, 269, 261, 254, 247, 242, 237, 231, 224, 219, 213, 209, 204, 200, 194, 189, 185, 181, 175, 170, 167, 163, 160, 156, 153, 149, 146, 143, 139, 135, 132, 129, 125, 121, 118, 116, 113, 110, 107, 104, 101, 99, 96, 94, 92, 90, 87, 85, 82, 80, 78, 76, 74, 72, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
# High DKL values for low BPP (high compression)
manual_dkl_per_step: [40, 40, 40, 78, 84, 134, 134, 136, 184, 184, 184, 146, 180, 244, 260, 258, 250, 204, 286, 270, 308, 202, 212, 266, 162, 264, 382, 268, 222, 258, 268, 310, 290, 266, 420, 366, 342, 398, 330, 342, 400, 466, 338, 298, 412, 486, 392, 406, 358, 432, 514, 468, 486, 358, 368, 456, 554, 408, 508, 348, 448, 368, 572, 494, 406, 418, 652, 564, 346, 476, 366, 502, 386, 530, 408, 420, 580, 600, 462, 476, 658, 684, 530, 362, 558, 576, 596, 616, 638, 438, 680, 466, 478, 490, 764, 526, 818, 564, 582, 600, 618, 640, 660, 684, 708, 736, 764, 794, 828, 862, 900, 942, 986, 1034, 534, 548, 562, 578, 592, 608, 626, 644, 664, 686, 708, 730, 756, 782, 810, 840, 872, 906, 944, 986, 1028, 1076, 1126, 1184, 1244, 1310, 1384, 1468, 1558, 1662, 1776, 1910, 2060, 2238, 2450, 2708, 2972, 3300, 3650, 4362, 9362, 20154, 42594]
recon_timesteps: [900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10]
denoising_timesteps: [981, 961, 941, 921, 901, 881, 861, 841, 821, 801, 781, 761, 741, 721, 701, 681, 661, 641, 621, 601, 581, 561, 541, 521, 501, 481, 461, 441, 421, 401, 381, 361, 341, 321, 301, 281, 261, 241, 221, 201, 181, 161, 141, 121, 101, 81, 61, 41, 21, 10, 5, 0]

# Recommended usage:
# python compress_improved.py --config configs/SD-1.5-low-bpp.yaml --recon_timestep 300
