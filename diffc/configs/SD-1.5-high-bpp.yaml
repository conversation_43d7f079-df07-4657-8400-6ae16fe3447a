# High BPP configuration - Lower compression, higher quality
max_chunk_size: 16
chunk_padding: 2
model: 'SD1.5'
encoding_guidance_scale: 0
denoising_guidance_scale: 0
encoding_timesteps: [972, 949, 929, 897, 869, 834, 805, 780, 751, 726, 704, 688, 670, 648, 627, 608, 591, 578, 561, 546, 530, 520, 510, 498, 491, 480, 465, 455, 447, 438, 429, 419, 410, 402, 390, 380, 371, 361, 353, 345, 336, 326, 319, 313, 305, 296, 289, 282, 276, 269, 261, 254, 247, 242, 237, 231, 224, 219, 213, 209, 204, 200, 194, 189, 185, 181, 175, 170, 167, 163, 160, 156, 153, 149, 146, 143, 139, 135, 132, 129, 125, 121, 118, 116, 113, 110, 107, 104, 101, 99, 96, 94, 92, 90, 87, 85, 82, 80, 78, 76, 74, 72, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
# Low DKL values for high BPP (low compression, high quality)
manual_dkl_per_step: [5, 5, 5, 10, 11, 17, 17, 17, 23, 23, 23, 18, 23, 31, 33, 32, 31, 26, 36, 34, 39, 25, 27, 33, 20, 33, 48, 34, 28, 32, 34, 39, 36, 33, 53, 46, 43, 50, 41, 43, 50, 58, 42, 37, 52, 61, 49, 51, 45, 54, 64, 59, 61, 45, 46, 57, 69, 51, 64, 44, 56, 46, 72, 62, 51, 52, 82, 71, 43, 60, 46, 63, 48, 66, 51, 53, 73, 75, 58, 60, 82, 86, 66, 45, 70, 72, 75, 77, 80, 55, 85, 58, 60, 61, 96, 66, 102, 71, 73, 75, 77, 80, 83, 86, 89, 92, 96, 99, 104, 108, 113, 118, 123, 129, 67, 69, 70, 72, 74, 76, 78, 81, 83, 86, 89, 91, 95, 98, 101, 105, 109, 113, 118, 123, 129, 134, 141, 148, 156, 164, 173, 184, 195, 208, 222, 239, 258, 281, 307, 339, 377, 425, 487, 571, 686, 856, 1174, 1823, 5324]
recon_timesteps: [900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10]
denoising_timesteps: [981, 961, 941, 921, 901, 881, 861, 841, 821, 801, 781, 761, 741, 721, 701, 681, 661, 641, 621, 601, 581, 561, 541, 521, 501, 481, 461, 441, 421, 401, 381, 361, 341, 321, 301, 281, 261, 241, 221, 201, 181, 161, 141, 121, 101, 81, 61, 41, 21, 10, 5, 0]

# Recommended usage:
# python compress_improved.py --config configs/SD-1.5-high-bpp.yaml --recon_timestep 100
