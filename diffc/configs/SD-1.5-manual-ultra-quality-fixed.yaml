# Ultra High Quality SD-1.5 configuration with ultra-low DKL values
# 250 steps for maximum quality with ultra-dense sampling

max_chunk_size: 16
chunk_padding: 2
model: 'SD1.5'
encoding_guidance_scale: 0
denoising_guidance_scale: 1.0

# Ultra-dense encoding timesteps (250 steps total)
encoding_timesteps: [995, 992, 989, 986, 983, 980, 977, 974, 971, 968, 965, 962, 959, 956, 953, 950, 947, 944, 941, 938, 935, 932, 929, 926, 923, 920, 917, 914, 911, 908, 905, 902, 899, 896, 893, 890, 887, 884, 881, 878, 875, 872, 869, 866, 863, 860, 857, 854, 851, 848, 845, 842, 839, 836, 833, 830, 827, 824, 821, 818, 815, 812, 809, 806, 803, 800, 797, 794, 791, 788, 785, 782, 779, 776, 773, 770, 767, 764, 761, 758, 755, 752, 749, 746, 743, 740, 737, 734, 731, 728, 725, 722, 719, 716, 713, 710, 707, 704, 701, 698, 695, 692, 689, 686, 683, 680, 677, 674, 671, 668, 665, 662, 659, 656, 653, 650, 647, 644, 641, 638, 635, 632, 629, 626, 623, 620, 617, 614, 611, 608, 605, 602, 599, 596, 593, 590, 587, 584, 581, 578, 575, 572, 569, 566, 563, 560, 557, 554, 551, 548, 545, 542, 539, 536, 533, 530, 527, 524, 521, 518, 515, 512, 509, 506, 503, 500, 497, 494, 491, 488, 485, 482, 479, 476, 473, 470, 467, 464, 461, 458, 455, 452, 449, 446, 443, 440, 437, 434, 431, 428, 425, 422, 419, 416, 413, 410, 407, 404, 401, 398, 395, 392, 389, 386, 383, 380, 377, 374, 371, 368, 365, 362, 359, 356, 353, 350, 347, 344, 341, 338, 335, 332, 329, 326, 323, 320, 317, 314, 311, 308, 305, 302, 299, 296, 293, 290, 287, 284, 281, 278, 275, 272, 269, 266, 263, 260, 257, 254, 251, 248, 245, 242, 239, 236, 233, 230, 227, 224, 221, 218, 215, 212, 209, 206, 203, 200, 197, 194, 191, 188, 185, 182, 179, 176, 173, 170, 167, 164, 161, 158, 155, 152, 149, 146, 143, 140, 137, 134, 131, 128, 125, 122, 119, 116, 113, 110, 107, 104, 101, 98, 95, 92, 89, 86, 83, 80, 77, 74, 71, 68, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2]

# Ultra-low DKL values (250 values to match timesteps exactly)
manual_dkl_per_step: [2, 2, 2, 3, 3, 4, 4, 4, 5, 5, 5, 4, 6, 7, 8, 7, 6, 5, 8, 7, 9, 5, 6, 8, 4, 8, 11, 8, 6, 7, 8, 9, 9, 8, 12, 10, 9, 11, 8, 9, 10, 13, 8, 7, 12, 14, 10, 12, 9, 13, 15, 11, 12, 9, 10, 11, 16, 10, 15, 8, 11, 10, 16, 12, 10, 11, 18, 15, 8, 12, 10, 15, 10, 16, 11, 11, 16, 17, 11, 12, 18, 19, 12, 9, 16, 17, 17, 18, 19, 11, 20, 11, 12, 12, 22, 13, 24, 13, 14, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170]

recon_timesteps: [900, 800, 700, 600, 500, 400, 350, 300, 250, 200, 175, 150, 125, 100, 75, 60, 50, 40, 30, 25, 20, 15, 10, 5]
denoising_timesteps: [999, 990, 981, 972, 963, 954, 945, 936, 927, 918, 909, 900, 891, 882, 873, 864, 855, 846, 837, 828, 819, 810, 801, 792, 783, 774, 765, 756, 747, 738, 729, 720, 711, 702, 693, 684, 675, 666, 657, 648, 639, 630, 621, 612, 603, 594, 585, 576, 567, 558, 549, 540, 531, 522, 513, 504, 495, 486, 477, 468, 459, 450, 441, 432, 423, 414, 405, 396, 387, 378, 369, 360, 351, 342, 333, 324, 315, 306, 297, 288, 279, 270, 261, 252, 243, 234, 225, 216, 207, 198, 189, 180, 171, 162, 153, 144, 135, 126, 117, 108, 99, 90, 81, 72, 63, 54, 45, 36, 27, 18, 9, 0]

# Usage: python compress_improved.py --config configs/SD-1.5-manual-ultra-quality-fixed.yaml --recon_timestep 50
