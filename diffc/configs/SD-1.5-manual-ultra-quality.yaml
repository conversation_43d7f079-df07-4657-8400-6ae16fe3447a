# Ultra High Quality SD-1.5 configuration with ultra-low DKL values
# Designed for maximum quality with minimal compression artifacts

max_chunk_size: 16
chunk_padding: 2
model: 'SD1.5'
encoding_guidance_scale: 0
denoising_guidance_scale: 1.0  # Slightly higher for better quality

# Standard encoding timesteps
encoding_timesteps: [972, 949, 929, 897, 869, 834, 805, 780, 751, 726, 704, 688, 670, 648, 627, 608, 591, 578, 561, 546, 530, 520, 510, 498, 491, 480, 465, 455, 447, 438, 429, 419, 410, 402, 390, 380, 371, 361, 353, 345, 336, 326, 319, 313, 305, 296, 289, 282, 276, 269, 261, 254, 247, 242, 237, 231, 224, 219, 213, 209, 204, 200, 194, 189, 185, 181, 175, 170, 167, 163, 160, 156, 153, 149, 146, 143, 139, 135, 132, 129, 125, 121, 118, 116, 113, 110, 107, 104, 101, 99, 96, 94, 92, 90, 87, 85, 82, 80, 78, 76, 74, 72, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]

# Ultra-low DKL values for maximum quality preservation
# Strategy: Minimize compression artifacts at all stages
manual_dkl_per_step: [
  # Steps 1-20: Extremely low DKL for perfect coarse structure
  3, 3, 3, 5, 6, 7, 7, 8, 9, 9,
  9, 7, 10, 12, 13, 12, 11, 9, 14, 13,
  # Steps 21-40: Very conservative increase
  15, 9, 10, 13, 8, 13, 18, 13, 11, 12,
  13, 14, 14, 13, 19, 17, 16, 18, 15, 16,
  # Steps 41-60: Still very low for mid-level details
  18, 21, 15, 14, 19, 22, 18, 19, 17, 20,
  23, 21, 22, 17, 18, 21, 25, 19, 23, 16,
  # Steps 61-80: Gradual but conservative increase
  21, 18, 26, 22, 19, 20, 29, 25, 16, 22,
  18, 23, 18, 24, 19, 20, 26, 27, 21, 22,
  # Steps 81-100: Maintaining ultra-high quality
  29, 30, 23, 17, 25, 26, 27, 28, 29, 20,
  30, 21, 22, 23, 34, 24, 36, 25, 26, 27,
  # Steps 101-120: Fine detail preservation with minimal loss
  28, 29, 30, 31, 32, 34, 35, 37, 38, 40,
  42, 44, 46, 48, 50, 52, 54, 56, 58, 60,
  # Steps 121-140: Moderate compression for less critical areas
  62, 64, 66, 68, 70, 72, 74, 76, 78, 80,
  82, 84, 86, 88, 90, 92, 94, 96, 98, 100,
  # Steps 141-161: Final steps with controlled compression
  102, 104, 106, 108, 110, 112, 114, 116, 118, 120,
  125, 130, 135, 140, 145, 150, 155, 160, 165, 170,
  180
]

# More reconstruction timesteps for detailed evaluation
recon_timesteps: [900, 800, 700, 600, 500, 400, 350, 300, 250, 200, 175, 150, 125, 100, 75, 60, 50, 40, 30, 25, 20, 15, 10, 5]

# Maximum denoising steps for best reconstruction
denoising_timesteps: [999, 990, 981, 972, 963, 954, 945, 936, 927, 918, 909, 900, 891, 882, 873, 864, 855, 846, 837, 828, 819, 810, 801, 792, 783, 774, 765, 756, 747, 738, 729, 720, 711, 702, 693, 684, 675, 666, 657, 648, 639, 630, 621, 612, 603, 594, 585, 576, 567, 558, 549, 540, 531, 522, 513, 504, 495, 486, 477, 468, 459, 450, 441, 432, 423, 414, 405, 396, 387, 378, 369, 360, 351, 342, 333, 324, 315, 306, 297, 288, 279, 270, 261, 252, 243, 234, 225, 216, 207, 198, 189, 180, 171, 162, 153, 144, 135, 126, 117, 108, 99, 90, 81, 72, 63, 54, 45, 36, 27, 18, 9, 0]

# Optimal usage for ultra-high quality:
# --recon_timestep 25-50 for maximum quality
# --recon_timestep 75-100 for excellent quality with reasonable file size
# 
# Example commands:
# python compress_improved.py --config configs/SD-1.5-manual-ultra-quality.yaml --recon_timestep 40 --image_dir data/kodak --output_dir results/ultra-hq
# python decompress_improved.py --config configs/SD-1.5-manual-ultra-quality.yaml --input_dir results/ultra-hq --output_dir results/ultra-hq/recon
