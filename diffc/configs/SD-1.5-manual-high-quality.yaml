# High Quality SD-1.5 configuration with manually optimized DKL values
# Designed for high BPP (low compression) with excellent reconstruction quality

max_chunk_size: 16
chunk_padding: 2
model: 'SD1.5'
encoding_guidance_scale: 0
denoising_guidance_scale: 0  # Can increase to 1.0-2.0 for even better quality

# Standard encoding timesteps (161 steps total)
encoding_timesteps: [972, 949, 929, 897, 869, 834, 805, 780, 751, 726, 704, 688, 670, 648, 627, 608, 591, 578, 561, 546, 530, 520, 510, 498, 491, 480, 465, 455, 447, 438, 429, 419, 410, 402, 390, 380, 371, 361, 353, 345, 336, 326, 319, 313, 305, 296, 289, 282, 276, 269, 261, 254, 247, 242, 237, 231, 224, 219, 213, 209, 204, 200, 194, 189, 185, 181, 175, 170, 167, 163, 160, 156, 153, 149, 146, 143, 139, 135, 132, 129, 125, 121, 118, 116, 113, 110, 107, 104, 101, 99, 96, 94, 92, 90, 87, 85, 82, 80, 78, 76, 74, 72, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]

# Manually optimized DKL values for high quality reconstruction
# Strategy: Lower DKL values throughout, with careful progression
# Early steps (high timesteps): Very low DKL to preserve coarse structure
# Middle steps: Gradual increase to balance quality and compression
# Late steps (low timesteps): Moderate DKL to preserve fine details
manual_dkl_per_step: [
  # Steps 1-20: Very low DKL for coarse structure preservation
  8, 8, 8, 12, 15, 18, 18, 20, 22, 22, 
  22, 18, 25, 30, 32, 30, 28, 24, 35, 32, 
  # Steps 21-40: Gradual increase
  38, 24, 26, 32, 20, 32, 45, 32, 28, 30, 
  32, 36, 34, 32, 48, 42, 40, 46, 38, 40, 
  # Steps 41-60: Moderate values for mid-level details
  46, 52, 38, 34, 48, 55, 45, 48, 42, 50, 
  58, 52, 55, 42, 44, 52, 62, 48, 58, 40, 
  # Steps 61-80: Balanced progression
  52, 44, 65, 56, 48, 50, 72, 62, 40, 55, 
  44, 58, 45, 60, 48, 50, 65, 68, 52, 55, 
  # Steps 81-100: Maintaining quality in important range
  72, 75, 58, 42, 62, 65, 68, 70, 72, 50, 
  75, 52, 55, 58, 85, 60, 90, 62, 65, 68, 
  # Steps 101-120: Fine detail preservation
  70, 72, 75, 78, 80, 85, 88, 92, 95, 100, 
  105, 110, 115, 120, 125, 130, 135, 140, 145, 150, 
  # Steps 121-140: Higher compression for less critical details
  155, 160, 165, 170, 175, 180, 185, 190, 195, 200, 
  205, 210, 215, 220, 225, 230, 235, 240, 245, 250, 
  # Steps 141-161: Final steps with controlled compression
  255, 260, 265, 270, 275, 280, 285, 290, 295, 300, 
  310, 320, 330, 340, 350, 360, 370, 380, 390, 400, 
  420
]

# Reconstruction timesteps for evaluation
recon_timesteps: [900, 800, 700, 600, 500, 400, 300, 200, 150, 100, 75, 50, 40, 30, 20, 10]

# Enhanced denoising timesteps for better reconstruction
denoising_timesteps: [981, 961, 941, 921, 901, 881, 861, 841, 821, 801, 781, 761, 741, 721, 701, 681, 661, 641, 621, 601, 581, 561, 541, 521, 501, 481, 461, 441, 421, 401, 381, 361, 341, 321, 301, 281, 261, 241, 221, 201, 181, 161, 141, 121, 101, 81, 61, 41, 21, 10, 5, 0]

# Usage recommendations:
# For highest quality: --recon_timestep 50
# For balanced quality: --recon_timestep 100  
# For good quality: --recon_timestep 150
# 
# Example commands:
# python compress_improved.py --config configs/SD-1.5-manual-high-quality.yaml --recon_timestep 75 --image_dir data/kodak --output_dir results/manual-hq
# python decompress_improved.py --config configs/SD-1.5-manual-high-quality.yaml --input_dir results/manual-hq --output_dir results/manual-hq/recon
