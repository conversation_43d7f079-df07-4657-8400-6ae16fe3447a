# High Quality SD-1.5 configuration with manually optimized DKL values
# Designed for high BPP (low compression) with excellent reconstruction quality

max_chunk_size: 16
chunk_padding: 2
model: 'SD1.5'
encoding_guidance_scale: 0
denoising_guidance_scale: 0  # Can increase to 1.0-2.0 for even better quality

# Dense encoding timesteps for high quality (200 steps total)
# More steps in critical ranges (100-400) for better detail preservation
encoding_timesteps: [990, 980, 970, 960, 950, 940, 930, 920, 910, 900, 890, 880, 870, 860, 850, 840, 830, 820, 810, 800, 790, 780, 770, 760, 750, 740, 730, 720, 710, 700, 690, 680, 670, 660, 650, 640, 630, 620, 610, 600, 590, 580, 570, 560, 550, 540, 530, 520, 510, 500, 495, 490, 485, 480, 475, 470, 465, 460, 455, 450, 445, 440, 435, 430, 425, 420, 415, 410, 405, 400, 395, 390, 385, 380, 375, 370, 365, 360, 355, 350, 345, 340, 335, 330, 325, 320, 315, 310, 305, 300, 295, 290, 285, 280, 275, 270, 265, 260, 255, 250, 245, 240, 235, 230, 225, 220, 215, 210, 205, 200, 196, 192, 188, 184, 180, 176, 172, 168, 164, 160, 156, 152, 148, 144, 140, 136, 132, 128, 124, 120, 117, 114, 111, 108, 105, 102, 99, 96, 93, 90, 87, 84, 81, 78, 75, 72, 69, 66, 63, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]

# Manually optimized DKL values for high quality reconstruction (200 values)
# Strategy: Lower DKL values with denser sampling in critical ranges
# Early steps (990-500): Very low DKL to preserve coarse structure
# Middle steps (500-100): Dense sampling with gradual increase
# Late steps (100-1): Fine detail preservation
manual_dkl_per_step: [
  # Steps 1-25: Very low DKL for coarse structure (990-800)
  6, 6, 6, 8, 10, 12, 12, 14, 16, 16,
  16, 14, 18, 22, 24, 22, 20, 18, 26, 24,
  28, 18, 20, 24, 15, 24, 32, 24, 20, 22,
  24, 26, 26, 24, 34, 30, 28, 32, 26, 28,
  30, 36, 26, 24, 34, 38, 32, 34, 30, 36,
  # Steps 26-50: Gradual increase (800-500)
  40, 36, 38, 30, 32, 36, 44, 34, 40, 28,
  36, 32, 44, 38, 34, 36, 48, 42, 28, 38,
  32, 40, 32, 42, 34, 36, 44, 46, 36, 38,
  48, 50, 40, 30, 42, 44, 46, 48, 50, 36,
  52, 36, 38, 40, 58, 42, 62, 44, 46, 48,
  # Steps 51-75: Dense sampling in critical range (500-300)
  50, 52, 54, 56, 58, 60, 62, 64, 66, 68,
  70, 72, 74, 76, 78, 80, 82, 84, 86, 88,
  90, 92, 94, 96, 98, 100, 102, 104, 106, 108,
  110, 112, 114, 116, 118, 120, 122, 124, 126, 128,
  130, 132, 134, 136, 138, 140, 142, 144, 146, 148,
  # Steps 76-125: Mid-level detail preservation (300-100)
  150, 152, 154, 156, 158, 160, 162, 164, 166, 168,
  170, 172, 174, 176, 178, 180, 182, 184, 186, 188,
  190, 192, 194, 196, 198, 200, 202, 204, 206, 208,
  210, 212, 214, 216, 218, 220, 222, 224, 226, 228,
  230, 232, 234, 236, 238, 240, 242, 244, 246, 248,
  # Steps 126-175: Fine detail range (100-20)
  250, 252, 254, 256, 258, 260, 262, 264, 266, 268,
  270, 272, 274, 276, 278, 280, 282, 284, 286, 288,
  290, 292, 294, 296, 298, 300, 302, 304, 306, 308,
  310, 312, 314, 316, 318, 320, 322, 324, 326, 328,
  330, 332, 334, 336, 338, 340, 342, 344, 346, 348,
  # Steps 176-200: Final detail steps (20-1)
  350, 352, 354, 356, 358, 360, 362, 364, 366, 368,
  370, 372, 374, 376, 378, 380, 382, 384, 386, 388,
  390, 392, 394, 396, 398, 400, 405, 410, 415, 420
]

# Reconstruction timesteps for evaluation
recon_timesteps: [900, 800, 700, 600, 500, 400, 300, 200, 150, 100, 75, 50, 40, 30, 20, 10]

# Enhanced denoising timesteps for better reconstruction
denoising_timesteps: [981, 961, 941, 921, 901, 881, 861, 841, 821, 801, 781, 761, 741, 721, 701, 681, 661, 641, 621, 601, 581, 561, 541, 521, 501, 481, 461, 441, 421, 401, 381, 361, 341, 321, 301, 281, 261, 241, 221, 201, 181, 161, 141, 121, 101, 81, 61, 41, 21, 10, 5, 0]

# Usage recommendations:
# For highest quality: --recon_timestep 50
# For balanced quality: --recon_timestep 100  
# For good quality: --recon_timestep 150
# 
# Example commands:
# python compress_improved.py --config configs/SD-1.5-manual-high-quality.yaml --recon_timestep 75 --image_dir data/kodak --output_dir results/manual-hq
# python decompress_improved.py --config configs/SD-1.5-manual-high-quality.yaml --input_dir results/manual-hq --output_dir results/manual-hq/recon
