# Ultra-high quality SD-1.5 configuration
max_chunk_size: 16
chunk_padding: 2
model: 'SD1.5'
encoding_guidance_scale: 0
denoising_guidance_scale: 0  # Can try 1.0-2.0 for better quality
encoding_timesteps: [972, 949, 929, 897, 869, 834, 805, 780, 751, 726, 704, 688, 670, 648, 627, 608, 591, 578, 561, 546, 530, 520, 510, 498, 491, 480, 465, 455, 447, 438, 429, 419, 410, 402, 390, 380, 371, 361, 353, 345, 336, 326, 319, 313, 305, 296, 289, 282, 276, 269, 261, 254, 247, 242, 237, 231, 224, 219, 213, 209, 204, 200, 194, 189, 185, 181, 175, 170, 167, 163, 160, 156, 153, 149, 146, 143, 139, 135, 132, 129, 125, 121, 118, 116, 113, 110, 107, 104, 101, 99, 96, 94, 92, 90, 87, 85, 82, 80, 78, 76, 74, 72, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
# Use adaptive DKL for best quality
manual_dkl_per_step: null
recon_timesteps: [900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10]
# More denoising steps for better quality
denoising_timesteps: [999, 990, 981, 972, 963, 954, 945, 936, 927, 918, 909, 900, 891, 882, 873, 864, 855, 846, 837, 828, 819, 810, 801, 792, 783, 774, 765, 756, 747, 738, 729, 720, 711, 702, 693, 684, 675, 666, 657, 648, 639, 630, 621, 612, 603, 594, 585, 576, 567, 558, 549, 540, 531, 522, 513, 504, 495, 486, 477, 468, 459, 450, 441, 432, 423, 414, 405, 396, 387, 378, 369, 360, 351, 342, 333, 324, 315, 306, 297, 288, 279, 270, 261, 252, 243, 234, 225, 216, 207, 198, 189, 180, 171, 162, 153, 144, 135, 126, 117, 108, 99, 90, 81, 72, 63, 54, 45, 36, 27, 18, 9, 0]
