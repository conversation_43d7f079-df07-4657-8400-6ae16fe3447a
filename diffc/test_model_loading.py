#!/usr/bin/env python3
"""
Test script to diagnose model loading issues
"""

import sys
import torch
import os
from pathlib import Path

def test_basic_imports():
    """Test basic library imports"""
    print("🔍 Testing basic imports...")
    try:
        import diffusers
        print(f"   ✅ diffusers version: {diffusers.__version__}")
    except ImportError as e:
        print(f"   ❌ diffusers import failed: {e}")
        return False
    
    try:
        import transformers
        print(f"   ✅ transformers version: {transformers.__version__}")
    except ImportError as e:
        print(f"   ❌ transformers import failed: {e}")
        return False
    
    print(f"   ✅ PyTorch version: {torch.__version__}")
    print(f"   ✅ CUDA available: {torch.cuda.is_available()}")
    
    return True

def test_huggingface_connection():
    """Test HuggingFace Hub connection"""
    print("\n🌐 Testing HuggingFace Hub connection...")
    try:
        from huggingface_hub import HfApi
        api = HfApi()
        # Test with a small model
        model_info = api.model_info("runwayml/stable-diffusion-v1-5")
        print(f"   ✅ Successfully connected to HuggingFace Hub")
        print(f"   ✅ Model info retrieved for SD 1.5")
        return True
    except Exception as e:
        print(f"   ❌ HuggingFace Hub connection failed: {e}")
        return False

def test_model_loading():
    """Test loading SD 1.5 model"""
    print("\n🤖 Testing SD 1.5 model loading...")
    try:
        from diffusers import StableDiffusionPipeline
        
        print("   🔄 Loading SD 1.5 pipeline (this may take a while)...")
        pipe = StableDiffusionPipeline.from_pretrained(
            "runwayml/stable-diffusion-v1-5",
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            low_cpu_mem_usage=True
        )
        print("   ✅ SD 1.5 pipeline loaded successfully")
        
        # Test moving to device
        device = "cuda" if torch.cuda.is_available() else "cpu"
        pipe = pipe.to(device)
        print(f"   ✅ Pipeline moved to {device}")
        
        return True
    except Exception as e:
        print(f"   ❌ SD 1.5 model loading failed: {e}")
        return False

def test_custom_model_loading():
    """Test loading our custom SD15Model"""
    print("\n🔧 Testing custom SD15Model loading...")
    try:
        sys.path.append('lib')
        from lib.models.SD15 import SD15Model
        
        print("   🔄 Creating SD15Model instance...")
        device = "cuda" if torch.cuda.is_available() else "cpu"
        dtype = torch.float16 if torch.cuda.is_available() else torch.float32
        
        model = SD15Model(device=device, dtype=dtype)
        print("   ✅ SD15Model created successfully")
        
        # Test basic functionality
        print("   🔄 Testing model configuration...")
        model.configure("test prompt", 0.0, 512, 512)
        print("   ✅ Model configuration successful")
        
        return True
    except Exception as e:
        print(f"   ❌ Custom SD15Model loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_disk_space():
    """Check available disk space"""
    print("\n💾 Checking disk space...")
    try:
        # Check current directory
        statvfs = os.statvfs('.')
        free_space_gb = (statvfs.f_frsize * statvfs.f_bavail) / (1024**3)
        print(f"   📊 Free space in current directory: {free_space_gb:.2f} GB")
        
        # Check HuggingFace cache directory
        hf_cache = Path.home() / '.cache' / 'huggingface'
        if hf_cache.exists():
            print(f"   📁 HuggingFace cache directory: {hf_cache}")
        else:
            print(f"   📁 HuggingFace cache directory not found (will be created)")
        
        if free_space_gb < 10:
            print("   ⚠️  Warning: Less than 10 GB free space available")
            print("   💡 SD models require 4-12 GB each")
        else:
            print("   ✅ Sufficient disk space available")
            
        return True
    except Exception as e:
        print(f"   ❌ Disk space check failed: {e}")
        return False

def main():
    """Run all diagnostic tests"""
    print("🚀 DiffC Model Loading Diagnostic")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Disk Space", check_disk_space),
        ("HuggingFace Connection", test_huggingface_connection),
        ("SD 1.5 Model Loading", test_model_loading),
        ("Custom SD15Model", test_custom_model_loading),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"   ❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 All tests passed! Model loading should work correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
        print("\n💡 Common solutions:")
        print("   - Check internet connection")
        print("   - Free up disk space (need 10+ GB)")
        print("   - Update diffusers: pip install --upgrade diffusers")
        print("   - Set HuggingFace cache: export HF_HOME=/path/to/large/storage")

if __name__ == "__main__":
    main()
