#!/usr/bin/env python3
"""
Decompress images using semantic-aware DiffC with real DeepLabV3Plus model
"""

import argparse
import sys
import os
from pathlib import Path
import yaml
from PIL import Image
import torch
import numpy as np
import zlib
import struct

# Add DeepLabV3Plus-Pytorch to path
sys.path.append('../DeepLabV3Plus-Pytorch')

try:
    import network
    from datasets.cityscapes import Cityscapes
    import torchvision.transforms as T
except ImportError as e:
    print(f"❌ Failed to import DeepLabV3Plus modules: {e}")
    print("Please make sure DeepLabV3Plus-Pytorch is in the parent directory")
    sys.exit(1)

from lib.semantic.segmentation import SemanticSegmentationModel

class DeepLabV3PlusSemanticModel(SemanticSegmentationModel):
    """DeepLabV3Plus model integrated with the semantic segmentation interface"""
    
    def __init__(self, checkpoint_path=None, device='cpu'):
        self.device = device
        self.num_classes = 19
        
        # Create DeepLabV3Plus model
        print("🔄 Loading DeepLabV3Plus MobileNet model...")
        self.model = network.modeling.deeplabv3plus_mobilenet(
            num_classes=self.num_classes, 
            output_stride=16
        )
        
        # Load checkpoint
        if checkpoint_path and os.path.isfile(checkpoint_path):
            print(f"📥 Loading checkpoint: {checkpoint_path}")
            checkpoint = torch.load(checkpoint_path, map_location=torch.device('cpu'), weights_only=False)
            self.model.load_state_dict(checkpoint["model_state"])
            print("✅ Checkpoint loaded successfully")
        else:
            print("⚠️  No checkpoint provided, using random weights")
        
        self.model.eval()
        self.model.to(self.device)
        
        # Setup transforms
        self.transform = T.Compose([
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    def predict(self, image):
        """Predict semantic segmentation mask for an image"""
        if isinstance(image, Image.Image):
            original_size = image.size
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
        else:
            raise ValueError("Input must be a PIL Image")
        
        with torch.no_grad():
            outputs = self.model(image_tensor)
            pred = outputs.max(1)[1].cpu().squeeze(0)
            
            # Resize prediction to original image size if needed
            if pred.shape != (original_size[1], original_size[0]):  # (H, W) vs (W, H)
                pred_pil = Image.fromarray(pred.numpy().astype(np.uint8))
                pred_pil = pred_pil.resize(original_size, Image.NEAREST)
                pred = torch.from_numpy(np.array(pred_pil))
            
            return pred

def read_diffc_file(file_path):
    """Read compressed data from .diffc file format"""
    with open(file_path, 'rb') as f:
        # Read caption length (4 bytes)
        caption_length = struct.unpack('<I', f.read(4))[0]
        
        # Read width, height, and step_idx (2 bytes each)
        width = struct.unpack('<H', f.read(2))[0]
        height = struct.unpack('<H', f.read(2))[0]
        step_idx = struct.unpack('<H', f.read(2))[0]
        
        # Read and decompress caption
        compressed_caption = f.read(caption_length)
        caption = zlib.decompress(compressed_caption).decode('utf-8')
        
        # Read remaining bytes for image data
        image_bytes = list(f.read())
    
    return caption, width, height, step_idx, image_bytes

def create_deeplabv3plus_semantic_model():
    """Create DeepLabV3Plus semantic model with checkpoint"""
    # Find checkpoint
    checkpoint_paths = [
        "models/best_deeplabv3plus_mobilenet_cityscapes_os16.pth",
        "models/cityscapes/best_deeplabv3plus_mobilenet_cityscapes_os16.pth",
        "../models/best_deeplabv3plus_mobilenet_cityscapes_os16.pth",
    ]
    
    checkpoint_path = None
    for path in checkpoint_paths:
        if os.path.isfile(path):
            checkpoint_path = path
            break
    
    if not checkpoint_path:
        print("⚠️  No DeepLabV3Plus checkpoint found, using random weights")
    
    return DeepLabV3PlusSemanticModel(checkpoint_path=checkpoint_path, device='cpu')

def get_noise_prediction_model(model_name, config):
    """Get noise prediction model based on config"""
    if model_name == "SD1.5":
        from lib.models.SD15 import SD15Model
        return SD15Model()
    elif model_name == "SD2.1":
        from lib.models.SD21 import SD21Model
        return SD21Model()
    elif model_name == "SDXL":
        from lib.models.SDXL import SDXLModel
        use_refiner = config.get("use_refiner", False)
        return SDXLModel(use_refiner=use_refiner)
    elif model_name == 'Flux':
        from lib.models.Flux import FluxModel
        return FluxModel()
    else:
        raise ValueError(f"Unrecognized model: {model_name}")

def decompress_with_semantic_awareness(input_path, output_dir):
    """Decompress .diffc file using semantic-aware DiffC with DeepLabV3Plus"""
    print("🚀 SEMANTIC-AWARE DECOMPRESSION WITH DEEPLABV3PLUS")
    print("=" * 60)
    
    # Check input file
    print(f"📂 Loading compressed file: {input_path}")
    if not os.path.isfile(input_path):
        print(f"❌ File not found: {input_path}")
        return False
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Read compressed data
        print("📖 Reading compressed data...")
        caption, width, height, step_idx, compressed_bytes = read_diffc_file(input_path)
        print(f"   Image size: {width}x{height}")
        print(f"   Step index: {step_idx}")
        print(f"   Caption: {caption}")
        
        # Load semantic config (hardcoded like compress_with_deeplabv3plus.py)
        config_path = "configs/SD-1.5-semantic.yaml"
        print(f"\n⚙️  Loading configuration: {config_path}")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Convert to easydict
        from easydict import EasyDict as edict
        config = edict(config)

        # Check if semantic awareness is enabled
        semantic_aware = getattr(config, 'semantic_aware', False)
        print(f"   Semantic awareness: {'enabled' if semantic_aware else 'disabled'}")

        # Import decompression modules
        from lib.diffc.denoise import denoise
        from lib.diffc.decode import decode
        from lib.diffc.rcc.gaussian_channel_simulator import GaussianChannelSimulator
        from lib import image_utils
        
        # Get noise prediction model
        print(f"🤖 Loading {config.model} model...")
        noise_prediction_model = get_noise_prediction_model(config.model, config)
        
        # Setup Gaussian channel simulator
        gaussian_channel_simulator = GaussianChannelSimulator(
            config.max_chunk_size, config.chunk_padding
        )
        
        # Configure model with caption
        noise_prediction_model.configure(
            caption, 
            config.denoising_guidance_scale,
            width,
            height
        )
        
        print("   ✅ Decompression setup complete")
        
        # For semantic-aware decompression, we need to reconstruct the DKL values
        # This is a limitation: we need the original semantic mask or stored DKL values
        print("   🔄 Preparing DKL values for decompression...")

        # Try to get base DKL schedule
        base_dkl_schedule = getattr(config, 'base_dkl_schedule', config.get('manual_dkl_per_step', None))
        if base_dkl_schedule is None:
            print("   ⚠️  No DKL schedule found, using default values")
            base_dkl_schedule = [50.0] * (step_idx + 1)
        else:
            base_dkl_schedule = base_dkl_schedule[:step_idx+1]

        # For now, use base DKL schedule for decompression
        # TODO: Store actual DKL values used during compression for perfect reconstruction
        print("   🔄 Decompressing chunk seeds...")
        chunk_seeds_per_step = gaussian_channel_simulator.decompress_chunk_seeds(
            compressed_bytes, base_dkl_schedule
        )
        
        timestep = config.encoding_timesteps[step_idx]
        print(f"   Using timestep: {timestep}")
        
        # Get the noisy reconstruction    
        print("   🔄 Decoding latent representation...")
        noisy_recon = decode(
            width,
            height,
            config.encoding_timesteps,
            noise_prediction_model,
            gaussian_channel_simulator,
            chunk_seeds_per_step,
            base_dkl_schedule,
            seed=0
        )
        
        # Denoise
        print("   🔄 Denoising...")
        recon_latent = denoise(
            noisy_recon,
            timestep,
            config.denoising_timesteps,
            noise_prediction_model
        )
        
        # Convert to image
        print("   🔄 Converting to image...")
        recon_img_pt = noise_prediction_model.latent_to_image(recon_latent)
        recon_image = image_utils.torch_to_pil_img(recon_img_pt)
        
        # Save decompressed image
        output_path = os.path.join(output_dir, f"decompressed_{Path(input_path).stem}.png")
        recon_image.save(output_path)
        print(f"   ✅ Decompressed image saved: {output_path}")
        

        
        return True
        
    except Exception as e:
        print(f"❌ Decompression failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def parse_args():
    parser = argparse.ArgumentParser(
        description="Decompress DiffC-compressed images with semantic awareness using DeepLabV3Plus"
    )
    parser.add_argument(
        "--input_path",
        required=True,
        help="Path to the .diffc file to decompress"
    )
    parser.add_argument(
        "--output_dir",
        required=True,
        help="Directory to output the decompressed images to"
    )

    return parser.parse_args()

def main():
    args = parse_args()
    
    success = decompress_with_semantic_awareness(
        args.input_path,
        args.output_dir
    )
    
    if success:
        print("\n🎉 Decompression completed successfully!")
    else:
        print("\n💥 Decompression failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
