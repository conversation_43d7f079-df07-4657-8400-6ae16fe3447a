#!/usr/bin/env python3
"""
Visualize the relationship between recon_timestep and BPP
"""

import yaml
from easydict import EasyDict as edict
import matplotlib.pyplot as plt
import numpy as np

def load_config(config_path):
    with open(config_path, "r") as f:
        return edict(yaml.safe_load(f))

def find_step_index(timestep, encoding_timesteps):
    """Find the step index for a given timestep"""
    for i, t in enumerate(encoding_timesteps):
        if t <= timestep:
            return i
    return len(encoding_timesteps) - 1

def estimate_bpp(step_idx, base_bpp_per_step=0.01):
    """Estimate BPP based on number of encoding steps"""
    return (step_idx + 1) * base_bpp_per_step

def main():
    # Load configuration
    config = load_config("configs/SD-1.5-base.yaml")
    encoding_timesteps = config.encoding_timesteps
    
    # Test different recon_timesteps
    test_timesteps = [400, 300, 200, 150, 100, 50, 25, 10]
    
    print("🔍 Timestep vs BPP Analysis")
    print("=" * 50)
    print(f"{'Timestep':<10} {'Step Index':<12} {'Encoding Steps':<15} {'Estimated BPP':<15}")
    print("-" * 50)
    
    timesteps = []
    step_indices = []
    encoding_steps = []
    estimated_bpps = []
    
    for timestep in test_timesteps:
        step_idx = find_step_index(timestep, encoding_timesteps)
        num_steps = step_idx + 1
        est_bpp = estimate_bpp(step_idx)
        
        print(f"{timestep:<10} {step_idx:<12} {num_steps:<15} {est_bpp:<15.3f}")
        
        timesteps.append(timestep)
        step_indices.append(step_idx)
        encoding_steps.append(num_steps)
        estimated_bpps.append(est_bpp)
    
    print("\n📊 Key Insights:")
    print(f"   • Higher timestep → Fewer encoding steps → Lower BPP")
    print(f"   • Lower timestep → More encoding steps → Higher BPP")
    print(f"   • Timestep range: {min(encoding_timesteps)} - {max(encoding_timesteps)}")
    print(f"   • Total possible steps: {len(encoding_timesteps)}")
    
    # Create visualization
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Plot 1: Timestep vs Encoding Steps
    ax1.plot(timesteps, encoding_steps, 'bo-', linewidth=2, markersize=8)
    ax1.set_xlabel('Recon Timestep')
    ax1.set_ylabel('Number of Encoding Steps')
    ax1.set_title('Timestep vs Encoding Steps')
    ax1.grid(True, alpha=0.3)
    ax1.invert_xaxis()  # Higher timestep on left
    
    # Add annotations
    for i, (ts, steps) in enumerate(zip(timesteps, encoding_steps)):
        if i % 2 == 0:  # Annotate every other point
            ax1.annotate(f'{ts}→{steps}', (ts, steps), 
                        xytext=(10, 10), textcoords='offset points',
                        fontsize=9, alpha=0.7)
    
    # Plot 2: Timestep vs Estimated BPP
    ax2.plot(timesteps, estimated_bpps, 'ro-', linewidth=2, markersize=8)
    ax2.set_xlabel('Recon Timestep')
    ax2.set_ylabel('Estimated BPP')
    ax2.set_title('Timestep vs BPP (Inverse Relationship)')
    ax2.grid(True, alpha=0.3)
    ax2.invert_xaxis()  # Higher timestep on left
    
    # Add annotations
    for i, (ts, bpp) in enumerate(zip(timesteps, estimated_bpps)):
        if i % 2 == 0:  # Annotate every other point
            ax2.annotate(f'{ts}→{bpp:.2f}', (ts, bpp), 
                        xytext=(10, 10), textcoords='offset points',
                        fontsize=9, alpha=0.7)
    
    plt.tight_layout()
    plt.savefig('timestep_bpp_relationship.png', dpi=150, bbox_inches='tight')
    print(f"\n📈 Visualization saved as 'timestep_bpp_relationship.png'")
    
    # Show detailed encoding timesteps
    print(f"\n🔍 Encoding Timesteps (first 20):")
    print(f"   {encoding_timesteps[:20]}")
    print(f"   ... (total {len(encoding_timesteps)} timesteps)")
    
    # Show the mapping for specific examples
    print(f"\n📋 Detailed Examples:")
    for timestep in [400, 200, 100, 50]:
        step_idx = find_step_index(timestep, encoding_timesteps)
        actual_timestep = encoding_timesteps[step_idx] if step_idx < len(encoding_timesteps) else encoding_timesteps[-1]
        print(f"   recon_timestep={timestep} → step_idx={step_idx} → actual_timestep={actual_timestep} → {step_idx+1} encoding steps")

if __name__ == "__main__":
    main()
