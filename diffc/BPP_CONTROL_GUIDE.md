# BPP (Bits Per Pixel) Control Guide

This guide explains how to control the compression rate (BPP) in DiffC to achieve different quality-size trade-offs.

## Understanding BPP

**BPP (Bits Per Pixel)** measures compression efficiency:
- **Higher BPP** = Larger files, better quality
- **Lower BPP** = Smaller files, lower quality
- **Typical ranges**: 0.1-2.0 BPP for lossy compression

## Main Control Parameters

### 1. `recon_timestep` (Primary Control)

The most important parameter for controlling BPP:

```bash
# Ultra-low BPP (~0.1-0.3 BPP)
python compress_improved.py --recon_timestep 400

# Low BPP (~0.3-0.6 BPP) 
python compress_improved.py --recon_timestep 300

# Medium BPP (~0.6-1.0 BPP)
python compress_improved.py --recon_timestep 200

# High BPP (~1.0-1.5 BPP)
python compress_improved.py --recon_timestep 100

# Ultra-high BPP (~1.5+ BPP)
python compress_improved.py --recon_timestep 50
```

### 2. `manual_dkl_per_step` (Fine Control)

DKL values control the rate-distortion trade-off:

```yaml
# For LOWER BPP (higher compression):
manual_dkl_per_step: [40, 40, 78, 84, ...]  # Double the values

# For HIGHER BPP (lower compression):
manual_dkl_per_step: [10, 10, 20, 21, ...]  # Halve the values

# For ADAPTIVE BPP (recommended):
manual_dkl_per_step: null  # Let system optimize
```

### 3. `max_chunk_size` (Minor Control)

Affects compression granularity:

```yaml
max_chunk_size: 8   # Lower BPP (more compression)
max_chunk_size: 16  # Default
max_chunk_size: 32  # Higher BPP (less compression)
```

## Pre-configured Settings

### Low BPP (High Compression)
```bash
python compress_improved.py \
  --config configs/SD-1.5-low-bpp.yaml \
  --recon_timestep 300 \
  --image_dir data/kodak \
  --output_dir results/low-bpp
```

### High BPP (High Quality)
```bash
python compress_improved.py \
  --config configs/SD-1.5-high-bpp.yaml \
  --recon_timestep 100 \
  --image_dir data/kodak \
  --output_dir results/high-bpp
```

### Adaptive BPP (Recommended)
```bash
python compress_improved.py \
  --config configs/SD-1.5-ultra-quality.yaml \
  --recon_timestep 150 \
  --image_dir data/kodak \
  --output_dir results/adaptive-bpp
```

## BPP Measurement

To measure actual BPP of compressed files:

```python
import os
from PIL import Image

def calculate_bpp(compressed_file, original_image):
    # Get compressed file size in bits
    compressed_bits = os.path.getsize(compressed_file) * 8
    
    # Get image dimensions
    img = Image.open(original_image)
    total_pixels = img.width * img.height
    
    # Calculate BPP
    bpp = compressed_bits / total_pixels
    return bpp

# Example usage
bpp = calculate_bpp("compressed.diffc", "original.png")
print(f"BPP: {bpp:.3f}")
```

## Optimization Strategies

### For Minimum File Size (Ultra-low BPP)
1. Use `recon_timestep 400-500`
2. Use `configs/SD-1.5-low-bpp.yaml`
3. Set `max_chunk_size: 8`
4. Accept quality degradation

### For Maximum Quality (High BPP)
1. Use `recon_timestep 50-100`
2. Use `configs/SD-1.5-high-bpp.yaml` or `manual_dkl_per_step: null`
3. Set `max_chunk_size: 32`
4. Accept larger file sizes

### For Balanced Quality-Size
1. Use `recon_timestep 150-200`
2. Use `manual_dkl_per_step: null` (adaptive)
3. Keep default `max_chunk_size: 16`
4. Test different timesteps to find sweet spot

## Rate-Distortion Curves

To generate rate-distortion curves for your dataset:

```bash
# Test multiple timesteps
for timestep in 50 100 150 200 250 300 350 400; do
    python compress_improved.py \
      --config configs/SD-1.5-ultra-quality.yaml \
      --recon_timestep $timestep \
      --image_dir data/kodak \
      --output_dir results/rd-curve/t$timestep
    
    python decompress_improved.py \
      --config configs/SD-1.5-ultra-quality.yaml \
      --input_dir results/rd-curve/t$timestep \
      --output_dir results/rd-curve/t$timestep/recon
done
```

## Tips

1. **Start with adaptive DKL** (`manual_dkl_per_step: null`) for best results
2. **Lower timesteps** generally give better quality at higher BPP
3. **Test on your specific images** - optimal settings vary by content
4. **Use improved scripts** for consistent DKL handling
5. **Monitor both file size and visual quality** when optimizing

## Troubleshooting

- **Files too large**: Increase `recon_timestep`, use low-BPP config
- **Quality too poor**: Decrease `recon_timestep`, use high-BPP config  
- **Inconsistent results**: Use `manual_dkl_per_step: null` for adaptive optimization
- **Need specific BPP**: Adjust `recon_timestep` incrementally until target is reached
