#!/usr/bin/env python3
"""
Improved compression script that saves actual DKL values to the compressed file
"""

import argparse
from PIL import Image
from pathlib import Path
import yaml
import zlib
import struct
import json
from easydict import EasyDict as edict

from lib import image_utils
from lib.diffc.encode import encode
from lib.diffc.rcc.gaussian_channel_simulator import GaussianChannelSimulator
from lib.blip import BlipCaptioner

def parse_args():
    parser = argparse.ArgumentParser(
        description="Compress an image or folder of images using the DiffC algorithm (improved version)."
    )
    parser.add_argument(
        "--config",
        help="Path to the compression config file",
        required=True
    )
    parser.add_argument(
        "--image_path",
        default=None,
        help="Path to a single image to compress"
    )
    parser.add_argument(
        "--image_dir",
        default=None,
        help="Path to a directory containing one or more images to compress"
    )
    parser.add_argument(
        "--output_dir",
        required=True,
        help="Directory to output the compressed files to"
    )
    parser.add_argument(
        "--recon_timestep",
        type=int,
        required=True,
        help="Timestep at which to save the encoded version"
    )
    return parser.parse_args()

def get_noise_prediction_model(model_name, config):
    if model_name == "SD1.5":
        from lib.models.SD15 import SD15Model
        return SD15Model()
    elif model_name == "SD2.1":
        from lib.models.SD21 import SD21Model
        return SD21Model()
    elif model_name == "SDXL":
        from lib.models.SDXL import SDXLModel
        use_refiner = config.get("use_refiner", False)
        return SDXLModel(use_refiner=use_refiner)
    elif model_name == 'Flux':
        from lib.models.Flux import FluxModel
        return FluxModel()
    else:
        raise ValueError(f"Unrecognized model: {model_name}")

def write_improved_diffc_file(caption, image_bytes, width, height, step_idx, dkl_values, output_path):
    """Write compressed data to .diffc file format with DKL values included"""
    # Compress caption with zlib
    compressed_caption = zlib.compress(caption.encode('utf-8'))
    caption_length = len(compressed_caption)
    
    # Serialize DKL values to JSON and compress
    dkl_json = json.dumps(dkl_values)
    compressed_dkl = zlib.compress(dkl_json.encode('utf-8'))
    dkl_length = len(compressed_dkl)
    
    # Write improved format:
    # - Magic number (4 bytes): "DIFC" to identify improved format
    # - Caption length (4 bytes)
    # - DKL data length (4 bytes) 
    # - Width, height, step_idx (2 bytes each)
    # - Compressed caption
    # - Compressed DKL values
    # - Image data
    with open(output_path, 'wb') as f:
        f.write(b'DIFC')  # Magic number for improved format
        f.write(struct.pack('<I', caption_length))
        f.write(struct.pack('<I', dkl_length))
        f.write(struct.pack('<H', width))
        f.write(struct.pack('<H', height))
        f.write(struct.pack('<H', step_idx))
        f.write(compressed_caption)
        f.write(compressed_dkl)
        f.write(bytes(image_bytes))

def compress_image(image_path, output_path, noise_prediction_model, 
                  gaussian_channel_simulator, config, caption=""):
    # Load and preprocess image
    img_pil = Image.open(image_path)
    img_width, img_height = img_pil.size
    gt_pt = image_utils.pil_to_torch_img(img_pil)
    gt_latent = noise_prediction_model.image_to_latent(gt_pt)
    
    # Configure model
    noise_prediction_model.configure(
        caption, config.encoding_guidance_scale, img_width, img_height
    )

    # Encode image
    chunk_seeds_per_step, Dkl_per_step, _, recon_step_indices = encode(
        gt_latent,
        config.encoding_timesteps,
        noise_prediction_model,
        gaussian_channel_simulator,
        config.manual_dkl_per_step,
        [config.recon_timestep]  # Only encode for the specified timestep
    )
    
    # Get the compressed representation
    step_idx = recon_step_indices[0]  # Only one step since we specified one timestep
    bytes_data = gaussian_channel_simulator.compress_chunk_seeds(
        chunk_seeds_per_step[: step_idx + 1], 
        Dkl_per_step[: step_idx + 1]
    )

    # Save the actual DKL values used during compression
    actual_dkl_values = Dkl_per_step[: step_idx + 1]
    
    write_improved_diffc_file(
        caption,
        bytes_data,
        img_width,
        img_height,
        step_idx,
        actual_dkl_values,  # Save actual DKL values
        output_path)
    
    print(f"✅ Compressed with actual DKL values: {actual_dkl_values[:5]}..." if len(actual_dkl_values) > 5 else f"✅ Compressed with DKL values: {actual_dkl_values}")

def main():
    args = parse_args()
    
    # Load config
    with open(args.config, "r") as f:
        config = edict(yaml.safe_load(f))
    
    # Set up output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)

    # Get image paths
    if not bool(args.image_path) ^ bool(args.image_dir):
        raise ValueError("Must specify exactly one of --image_path or --image_dir")

    image_paths = []
    if args.image_path:
        image_paths.append(Path(args.image_path))
    else:
        image_dir = Path(args.image_dir)
        image_paths = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
        image_paths = list(map(Path, image_paths))

    # Initialize models
    gaussian_channel_simulator = GaussianChannelSimulator(
        config.max_chunk_size, config.chunk_padding
    )
    noise_prediction_model = get_noise_prediction_model(config.model, config)
    
    # Initialize captioner if needed
    captioner = BlipCaptioner()

    # Process each image
    for image_path in image_paths:
        print(f"🔄 Processing {image_path.name}...")
        
        # Generate caption
        img_pil = Image.open(image_path).convert('RGB')
        caption = captioner.generate_caption(img_pil)
        print(f"   Caption: {caption}")
        
        # Set up output path
        output_path = output_dir / f"{image_path.stem}_t{args.recon_timestep}.diffc"
        
        # Compress image
        compress_image(
            image_path, 
            output_path, 
            noise_prediction_model, 
            gaussian_channel_simulator, 
            config, 
            caption
        )
        
        print(f"   💾 Saved: {output_path}")

    print(f"\n🎉 Compression completed! Files saved to {output_dir}")
    print("💡 Use decompress_improved.py to decompress these files")

if __name__ == "__main__":
    main()
