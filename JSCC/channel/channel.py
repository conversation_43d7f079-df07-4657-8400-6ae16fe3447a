import tensorflow as tf
import numpy as np
from sionna.phy.channel.tr38901 import CDL,PanelArray
from sionna.phy.channel import OFDMChannel,AWGN
from sionna.phy.ofdm import ResourceGrid, ResourceGridMapper,OFDMModulator,OFDMDemodulator
from sionna.phy.ofdm import RemoveNulledSubcarriers, LMMSEEqualizer
from sionna.phy.mimo import StreamManagement
from sionna.phy.utils import ebnodb2no
from sionna.phy.mapping import Demapper,Mapper


@tf.custom_gradient
def QuantizationOp(x, B):
    B     = tf.cast(B, tf.int32)
    step  = tf.pow(2.0, tf.cast(B, x.dtype))
    batch = tf.shape(x)[0]
    N     = tf.shape(x)[1]

    # --- 前向：均匀量化 + bitwise 展开 ---
    y_int = tf.cast(tf.floor(tf.clip_by_value(x, 0., 1.-1./step)*step), tf.int32)
    y_exp = tf.expand_dims(y_int, -1)                    # [B,N,1]
    masks = tf.bitwise.left_shift(tf.ones((1,), tf.int32),
                                  tf.range(B, dtype=tf.int32))
    bits  = tf.bitwise.bitwise_and(y_exp, masks)
    bits  = tf.cast(bits > 0, x.dtype)                   # 0/1
    bits  = tf.reverse(bits, axis=[-1])                  # MSB→LSB
    bits  = tf.reshape(bits, (batch, N * B))             # [B, N*B]

    def grad(dy):
        # [B, N*B] -> [B, N, B] -> reduce_mean
        dy = tf.reshape(dy, (batch, N, B))
        return tf.reduce_mean(dy, axis=-1), None

    return bits, grad


class CDL_Layer(tf.keras.layers.Layer):
    def __init__(self, config, **kwargs):
        super().__init__(**kwargs)
        self.config = config

        # 基站 & 用户阵列
        self.bs_array = PanelArray(
            num_rows_per_panel=4,
            num_cols_per_panel=4,
            polarization="dual",
            polarization_type="cross",
            antenna_pattern="38.901",
            carrier_frequency=config.carrier_freq
        )
        self.ut_array = PanelArray(
            num_rows_per_panel=1,
            num_cols_per_panel=1,
            polarization="single",
            polarization_type="V",
            antenna_pattern="38.901",
            carrier_frequency=config.carrier_freq
        )

        # 资源网格配置
        self.resource_grid = ResourceGrid(
            num_ofdm_symbols=config.num_ofdm_symbols,
            fft_size=config.fft_size,
            subcarrier_spacing=config.subcarrier_spacing,
            num_tx=config.num_tx,
            num_streams_per_tx=config.num_streams_per_tx,
            pilot_pattern="empty"
        )
        self.stream_mgmt = StreamManagement(np.array([[1]]), config.num_streams_per_tx)
        self.rg_mapper = ResourceGridMapper(self.resource_grid)


        # CDL 信道模型 & OFDM
        self.cdl = CDL(
            model="A",
            delay_spread=config.delay_spread,
            carrier_frequency=config.carrier_freq,
            ut_array=self.ut_array,
            bs_array=self.bs_array,
            direction="uplink"
        )
        self.ofdm_channel = OFDMChannel(
            self.cdl, self.resource_grid, return_channel=True
        )

        # 接收端均衡
        self.mmse_equalizer = LMMSEEqualizer(self.resource_grid, self.stream_mgmt)
        self.mapper = Mapper("qam",config.num_bits_per_symbol)
        self.demapper = Demapper("app","qam",config.num_bits_per_symbol)


    # def call(self, x, no):
    #     x = tf.reshape(x,(x.shape[0],1,1,-1))
    #     # x: [batch, feedback_bits]，需预处理成 tx 维度对齐的复数格式
    #     x = self.mapper(x)

    #     # 维度 reshape 仅在需要时进行，如你已确保对齐，可忽略
    #     x = self.rg_mapper(x)  # [B, symbols, fft, tx]
        
    #     y_ofdm, h_freq = self.ofdm_channel(x, no)  # CDL + OFDM + noise
    #     # 可选：均衡 + 解调（如果你后续需要解调）
    #     y_eq, no_eff = self.mmse_equalizer(y_ofdm, h_freq, 0, no)

    #     x = self.demapper(y_eq,no_eff)

    #     return x  # 或 y_eq, h_freq，具体看你是否要提前均衡
    
    def call(self, x, no, bits):
        no   = tf.convert_to_tensor(no,   tf.float32)
        bits = tf.convert_to_tensor(bits, tf.int32)

        @tf.custom_gradient
        def path(x_in):
            # ---------- 前向 ----------
            bitstream = QuantizationOp(x_in, bits)                 # [B, N*B]
            s = tf.reshape(bitstream, (tf.shape(x_in)[0], 1, 1, -1))
            s = self.rg_mapper(self.mapper(s))                     # 任意映射
            y_ofdm, h = self.ofdm_channel(s, no)                   # 信道
            y_eq, no_eff = self.mmse_equalizer(y_ofdm, h, 0, no)   # 均衡
            llr = self.demapper(y_eq, no_eff)                      # 软输出

            # ---------- 反向：自动聚合 ----------
            def backward(dy):
                batch = tf.shape(dy)[0]
                in_len = tf.shape(x_in)[-1]
                out_len = tf.shape(dy)[-1]
                factor = out_len // in_len          # 例：4096//1024 = 4
                dy = tf.reshape(dy, (batch, in_len, factor))
                grad_x = tf.reduce_mean(dy, axis=-1)  # [batch, in_len]
                return grad_x                         # no/bits 不求导

            return llr, backward

        return path(x)


class AWGN_Layer(tf.keras.layers.Layer):
    def __init__(self, config, **kwargs):
        super().__init__(**kwargs)
        self.config = config

        # 基站 & 用户阵列
        self.bs_array = PanelArray(
            num_rows_per_panel=4,
            num_cols_per_panel=4,
            polarization="dual",
            polarization_type="cross",
            antenna_pattern="38.901",
            carrier_frequency=config.carrier_freq
        )
        self.ut_array = PanelArray(
            num_rows_per_panel=1,
            num_cols_per_panel=1,
            polarization="single",
            polarization_type="V",
            antenna_pattern="38.901",
            carrier_frequency=config.carrier_freq
        )

        # 资源网格配置
        self.resource_grid = ResourceGrid(
            num_ofdm_symbols=config.num_ofdm_symbols,
            fft_size=config.fft_size,
            subcarrier_spacing=config.subcarrier_spacing,
            num_tx=config.num_tx,
            num_streams_per_tx=config.num_streams_per_tx,
            pilot_pattern="empty"
        )
        self.stream_mgmt = StreamManagement(np.array([[1]]), config.num_streams_per_tx)
        self.rg_mapper = ResourceGridMapper(self.resource_grid)
        # CDL 信道模型 & OFDM

        self.channel = AWGN()
        self.mapper = Mapper("qam",config.num_bits_per_symbol)
        self.demapper = Demapper("app","qam",config.num_bits_per_symbol)
        self.ofdm_modulator = OFDMModulator(cyclic_perfix_length = config.cp_length)
        self.ofdm_demodulator = OFDMDemodulator(fft_size=config.fft_size,l_min=0,cyclic_perfix_length = config.cp_length)


    def call(self, x, no):
        x = tf.reshape(x,(x.shape[0],1,1,-1))
        # x: [batch, feedback_bits]，需预处理成 tx 维度对齐的复数格式
        x = self.mapper(x)
        # 维度 reshape 仅在需要时进行，如你已确保对齐，可忽略
        x = self.rg_mapper(x)  # [B, symbols, fft, tx]
        x = self.ofdm_modulator(x)
        
        y_ofdm  = self.channel(x, no)  # AWGN

        x = self.ofdm_demodulator(y_ofdm)
        x = self.demapper(x,no)

        return x  # 或 y_eq, h_freq，具体看你是否要提前均衡