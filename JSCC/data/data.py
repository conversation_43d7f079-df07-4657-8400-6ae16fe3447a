import tensorflow as tf

def get_dataset(data, batch_size, shuffle=True):
    # 输入 data 维度: [batch, 32, 13] (复数)
    
    # 拆分实部和虚部，并合并为新维度 [batch, 2, 32, 13]
    data_real = tf.math.real(data)  # 实部 [batch, 32, 13]
    data_imag = tf.math.imag(data)  # 虚部 [batch, 32, 13]
    data_split = tf.stack([data_real, data_imag], axis=1)  # [batch, 2, 32, 13]

    # 创建数据集（输入和输出相同，因为是自编码器）
    ds = tf.data.Dataset.from_tensor_slices((data_split, data_split))
    
    if shuffle:
        ds = ds.shuffle(buffer_size=len(data), seed=42)
    ds = ds.batch(batch_size).prefetch(tf.data.AUTOTUNE)
    return ds