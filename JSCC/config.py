from datetime import datetime

class Config:
    def __init__(self):

        self.data_height = 13
        self.data_width = 32
        self.data_channel = 2    
        self.batch_size = 32
        self.epochs = 200
        self.test_interval = 10

        ### 优化器参数
        self.optimizer = "Adam"
        self.lr_decay_strategy = "Exp"  # 或 "Warm-up"
        self.steps_per_epoch = 3000  # 建议设为 NUM_SAMPLES // batch_sizes
        self.lr_decay_epochs = 20
        self.exp_decay_rate = 0.96
        self.feedback_bits = 128
        self.initial_lr = 0.00008
        self.currentDT = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.root = "/home/<USER>/CSI_xiaomi/JSCC"
        ### 说明备注
        self.notse = f"Transformer is not trainable in this fine tuning run based on Result_{self.feedback_bits}_{self.currentDT}"
        ### 模型结构参数
        # Transformer 编码器/解码器
        self.enc_TF_layers = 3
        self.dec_TF_layers = 3
        self.TF_heads = 6
        self.embedding_dim = self.data_width * self.data_channel * 6
        # 量化调制相关

        self.quantization_dim = 1
        self.quantization_bits = 2
        self.num_bits_per_symbol = 2

        ### 信道参数
        self.snr_dB = 0 ## (dB)
        self.channel_type = "CDL"  #“CDL” or "AWGN"

        ### 信道资源网格参数（OFDM/CDL）
        self.num_ofdm_symbols = 1
        self.num_tx_antennas = 1
        self.num_rx_antennas = 32
        self.num_tx = 1
        self.num_rx = 1
        self.num_streams_per_tx = 1
        self.fft_size = 64
        self.num_subcarriers = self.fft_size
        self.subcarrier_spacing = 15e3  # Hzs
        self.carrier_freq = 3.5e9       # Hz
        self.delay_spread = 100e-9      # 秒
        self.speed = 3.0                # m/s
        self.feedback_bits = 128
        self.cp_length = 0
        ### 模型保存路径
        self.ckpt_dir = f"{self.root}/ckpt/Result_{self.feedback_bits}_{self.snr_dB}dB_{self.currentDT}/"

