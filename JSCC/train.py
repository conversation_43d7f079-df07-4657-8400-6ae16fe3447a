# systme config
import os
if os.getenv("CUDA_VISIBLE_DEVICES") is None:
    gpu_num = 0  # 指定使用GPU ID为1
    os.environ["CUDA_VISIBLE_DEVICES"] = f"{gpu_num}"
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# tensorflow 
import tensorflow as tf
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        tf.config.experimental.set_memory_growth(gpus[0], True)
    except RuntimeError as e:
        print(e)
tf.get_logger().setLevel('ERROR')

from tqdm import tqdm
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import pickle

from model.model import JSCC_Model
from monitor.monitor import TrainingMonitor
from datetime import datetime
from config import Config   
import pdb
import numpy as np
import tensorflow as tf
from data.data import get_dataset
from loss.loss import Distortion,NMSE,SGCS

train_data = np.load('/home/<USER>/CSI_xiaomi/JSCC/dataset/train_set.npy')
test_data = np.load('/home/<USER>/CSI_xiaomi/JSCC/dataset/test_set.npy')

def init_log(log_path):
    with open(log_path, 'w') as f:
        f.write("epoch,step,train_loss,test_loss,test_mse,test_nmse,test_sgcs\n")

def log_metrics(log_path, epoch, step, train_loss, test_loss=None, test_mse=None, test_nmse=None, test_sgcs=None):
    with open(log_path, 'a') as f:
        f.write(f"{epoch},{step},{train_loss},{test_loss},{test_mse},{test_nmse},{test_sgcs}\n")

# 训练参数
config = Config()
train_batch_size = config.batch_size
epochs = config.epochs
eval_interval = config.test_interval  # 每隔多少epoch评估一次
steps_per_epoch = len(train_data) // train_batch_size

# 构建数据集
train_ds = get_dataset(train_data, train_batch_size, shuffle=True)
test_ds = get_dataset(test_data, train_batch_size, shuffle=False)

# 模型与优化器

model = JSCC_Model(config)
optimizer = tf.keras.optimizers.Adam()
distortion = Distortion()

# 日志文件
save_log = f"/home/<USER>/CSI_xiaomi/JSCC/history/train/snr_{config.snr_dB}_febits_{config.feedback_bits}_qabits{config.quantization_bits}_perbits{config.num_bits_per_symbol}_channel_{config.channel_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
os.makedirs(save_log, exist_ok=True)
log_path = os.path.join(save_log, "train_log.txt")
init_log(log_path)

@tf.function
def train_step(x, model, optimizer, snr_dB):
    with tf.GradientTape() as tape:
        y_hat = model(x, snr_dB=snr_dB,training=True)
        loss = tf.reduce_mean(1-SGCS()(x, y_hat))
    weights = tape.watched_variables()
    grads = tape.gradient(loss, weights)
    optimizer.apply_gradients(zip(grads, weights))
    return loss

def evaluate(model, test_ds, snr_dB):
    total_loss = 0
    total_mse = 0
    total_nmse = 0
    total_sgcs = 0
    n = 0
    for x_batch, _ in test_ds:
        y_hat = model(x_batch, snr_dB=snr_dB)
        metrics = distortion(x_batch, y_hat)
        mse = float(metrics['mse'].numpy())
        nmse = float(metrics['nmse'].numpy())
        sgcs = float(metrics['sgcs'].numpy())
        total_loss += mse * x_batch.shape[0]
        total_mse += mse * x_batch.shape[0]
        total_nmse += nmse * x_batch.shape[0]
        total_sgcs += sgcs * x_batch.shape[0]
        n += x_batch.shape[0]
    avg_loss = total_loss / n
    avg_mse = total_mse / n
    avg_nmse = total_nmse / n
    avg_sgcs = total_sgcs / n
    print(f"Test MSE: {avg_mse:.4f}, NMSE: {avg_nmse:.4f}, SGCS: {avg_sgcs:.4f}")
    return avg_loss, avg_mse, avg_nmse, avg_sgcs

# 训练主循环

interval_loss = 0
interval_count = 0
best_test_loss = float('inf')
# 训练主循环

for epoch in range(epochs):
    print(f'\nEpoch {epoch + 1}/{epochs}')
    train_ds = get_dataset(train_data, train_batch_size, shuffle=True)
    prog = tqdm(enumerate(train_ds.take(steps_per_epoch)), total=steps_per_epoch, desc=f"Epoch {epoch + 1}")

    epoch_loss = 0.0

    for step, (x_batch, _) in prog:
        snr_dB = tf.cast(config.snr_dB, dtype=tf.float32)
        loss = train_step(x_batch, model, optimizer, snr_dB)
        loss_val = loss.numpy()
        epoch_loss += loss_val

        # ✅ tqdm 实时显示 loss
        prog.set_postfix({'train_loss': f'{loss_val:.4f}'})

    # ✅ 记录平均训练 loss 到日志
    avg_train_loss = epoch_loss / steps_per_epoch
    log_metrics(log_path, epoch + 1, steps_per_epoch, avg_train_loss)

    # ✅ 每 eval_interval 次或最后一轮进行评估并保存
    if (epoch + 1) % eval_interval == 0 or (epoch + 1) == epochs:
        test_loss, test_mse, test_nmse, test_sgcs = evaluate(model, test_ds, snr_dB)
        log_metrics(log_path, epoch + 1, 0, avg_train_loss, test_loss, test_mse, test_nmse, test_sgcs)

        if test_loss < best_test_loss:
            best_test_loss = test_loss
            model.save_weights(save_log, "best")

# ✅ 保存最终模型
model.save_weights(save_log, "final")
