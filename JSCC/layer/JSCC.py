import tensorflow as tf
from tensorflow.keras import layers
import numpy as np
from layer.modules import MultiHeadAttention

class JSCC_Encoder(tf.keras.layers.Layer):
    def __init__(self, NUM_QUAN_DIM, NUM_FEEDBACK_BITS, NUM_QUAN_BITS,
                 EMBEDDING_DIM, NUM_HEAD, CYCLE_NUM_enc, **kwargs):
        super(JSCC_Encoder, self).__init__(**kwargs)

        self.NUM_QUAN_DIM = NUM_QUAN_DIM
        self.NUM_FEEDBACK_BITS = NUM_FEEDBACK_BITS
        self.NUM_QUAN_BITS = NUM_QUAN_BITS
        self.EMBEDDING_DIM = EMBEDDING_DIM
        self.NUM_HEAD = NUM_HEAD
        self.CYCLE_NUM_enc = CYCLE_NUM_enc

        # Embedding projection
        self.embedding_dense = layers.Dense(EMBEDDING_DIM)
        self.dropout = layers.Dropout(0.1)

        # Encoder blocks
        self.att_layers = [self._make_att_block() for _ in range(CYCLE_NUM_enc)]
        self.ffn_layers = [self._make_fc_block() for _ in range(CYCLE_NUM_enc)]

        self.final_norm = layers.LayerNormalization()
        self.dense_proj = layers.Dense(16)
        self.final_dropout = layers.Dropout(0.1)
        self.flatten = layers.Flatten()
        self.encoder_output = layers.Dense(
            int(NUM_FEEDBACK_BITS / NUM_QUAN_BITS * NUM_QUAN_DIM),
            activation='sigmoid'
        )
        
    

    def _make_att_block(self):
        return [
            layers.LayerNormalization(),
            MultiHeadAttention(head_num=self.NUM_HEAD),
            layers.Dropout(0.1),
            layers.Add()
        ]

    def _make_fc_block(self):
        return [
            layers.LayerNormalization(),
            layers.Dense(self.EMBEDDING_DIM * 4),
            layers.ReLU(),
            layers.Dropout(0.1),
            layers.Dense(self.EMBEDDING_DIM),
            layers.Dropout(0.1),
            layers.Add()
        ]

    def _positional_encoding(self, seq_len, embedding_dim):
        # seq_len 和 embedding_dim 都是 int 或 tf.Tensor
        pos = tf.range(seq_len, dtype=tf.float32)[:, tf.newaxis]  # (seq_len, 1)
        i = tf.range(embedding_dim, dtype=tf.float32)[tf.newaxis, :]  # (1, embedding_dim)
        angle_rates = 1 / tf.pow(10000.0, (2 * (i // 2)) / tf.cast(embedding_dim, tf.float32))
        angle_rads = pos * angle_rates  # (seq_len, embedding_dim)

        # 偶数下标用sin，奇数下标用cos
        sines = tf.sin(angle_rads[:, 0::2])
        cosines = tf.cos(angle_rads[:, 1::2])

        # 拼接
        pos_encoding = tf.concat([sines, cosines], axis=-1)
        pos_encoding = pos_encoding[tf.newaxis, ...]  # (1, seq_len, embedding_dim)
        return pos_encoding

    def call(self, inputs, training=False):
            # 输入形状: [batch, 2, subcarriers, symbols] (例如 [batch, 2, 32, 13])
        B = tf.shape(inputs)[0]
        C = tf.shape(inputs)[1]  # 2 (实部+虚部)
        H = tf.shape(inputs)[2]  # 例如 32
        W = tf.shape(inputs)[3]     # 例如 13
        # 步骤1: 转置维度 [B, C, H, W] -> [B, W, H, C]
        x = tf.transpose(inputs, [0, 3, 2, 1])  # 将 W 维度移到第1位

        # 步骤2: 合并 H 和 C -> [B, W, H*C]
        x = tf.reshape(x, [B, W, H * C])  # 输出形状 [B, 13, 64]
        # inputs: (batch, Tx, subband*2)
        x = self.embedding_dense(x)

        x *= tf.math.sqrt(tf.cast(self.EMBEDDING_DIM, tf.float32))

        pos_enc = self._positional_encoding(tf.shape(x)[1], self.EMBEDDING_DIM)
        x += pos_enc[:, :tf.shape(x)[1], :]
        x = self.dropout(x, training=training)

        for att_block, ffn_block in zip(self.att_layers, self.ffn_layers):
            shortcut = x
            x_norm = att_block[0](x)
            x_att = att_block[1](x_norm)[0]  # 解包 MultiHeadAttention 输出
            x_att = att_block[2](x_att, training=training)
            x = att_block[3]([shortcut, x_att])

            shortcut = x
            x = ffn_block[0](x)
            x = ffn_block[1](x)
            x = ffn_block[2](x)
            x = ffn_block[3](x, training=training)
            x = ffn_block[4](x)
            x = ffn_block[5](x, training=training)
            x = ffn_block[6]([shortcut, x])

        x = self.final_norm(x)
        x = self.dense_proj(x)
        x = self.final_dropout(x, training=training)
        x = self.flatten(x)
        x = self.encoder_output(x)
        return x
    
class JSCC_Decoder(tf.keras.layers.Layer):
    def __init__(self, NUM_QUAN_DIM, NUM_FEEDBACK_BITS, NUM_QUAN_BITS,
                 EMBEDDING_DIM, NUM_HEAD, CYCLE_NUM_dec, **kwargs):
        super(JSCC_Decoder, self).__init__(**kwargs)
        self.EMBEDDING_DIM = EMBEDDING_DIM
        self.NUM_HEAD = NUM_HEAD
        self.CYCLE_NUM_dec = CYCLE_NUM_dec

        self.dense_proj = layers.Dense(416)
        self.reshape_proj = layers.Reshape((13, 32))
        self.embedding_dense = layers.Dense(EMBEDDING_DIM)

        self.att_layers = [self._make_att_block() for _ in range(CYCLE_NUM_dec)]
        self.ffn_layers = [self._make_fc_block() for _ in range(CYCLE_NUM_dec)]

        self.final_norm = layers.LayerNormalization()
        self.output_dense = layers.Dense(32 * 2)
        self.dropout = layers.Dropout(0.1)

    def _make_att_block(self):
        return [
            layers.LayerNormalization(),
            MultiHeadAttention(head_num=self.NUM_HEAD),
            layers.Dropout(0.1),
            layers.Add()
        ]

    def _make_fc_block(self):
        return [
            layers.LayerNormalization(),
            layers.Dense(self.EMBEDDING_DIM * 6),
            layers.ReLU(),
            layers.Dropout(0.1),
            layers.Dense(self.EMBEDDING_DIM),
            layers.Dropout(0.1),
            layers.Add()
        ]

    def _positional_encoding(self, seq_len, embedding_dim):
        # seq_len 和 embedding_dim 都是 int 或 tf.Tensor
        pos = tf.range(seq_len, dtype=tf.float32)[:, tf.newaxis]  # (seq_len, 1)
        i = tf.range(embedding_dim, dtype=tf.float32)[tf.newaxis, :]  # (1, embedding_dim)
        angle_rates = 1 / tf.pow(10000.0, (2 * (i // 2)) / tf.cast(embedding_dim, tf.float32))
        angle_rads = pos * angle_rates  # (seq_len, embedding_dim)

        # 偶数下标用sin，奇数下标用cos
        sines = tf.sin(angle_rads[:, 0::2])
        cosines = tf.cos(angle_rads[:, 1::2])

        # 拼接
        pos_encoding = tf.concat([sines, cosines], axis=-1)
        pos_encoding = pos_encoding[tf.newaxis, ...]  # (1, seq_len, embedding_dim)
        return pos_encoding

    def call(self, inputs, training=False):

        x = self.dense_proj(inputs)

        x = self.reshape_proj(x)

        # Linear embedding
        x = self.embedding_dense(x)

        x *= tf.math.sqrt(tf.cast(self.EMBEDDING_DIM, tf.float32))

        # Positional encoding
        pos_enc = self._positional_encoding(tf.shape(x)[1], self.EMBEDDING_DIM)
        x += pos_enc[:, :tf.shape(x)[1], :]

        x = self.dropout(x, training=training)

        for att_block, ffn_block in zip(self.att_layers, self.ffn_layers):
            shortcut = x
            x_norm = att_block[0](x)
            x_att = att_block[1](x_norm)[0]  # 解包 MHA 输出
            x_att = att_block[2](x_att, training=training)
            x = att_block[3]([shortcut, x_att])  # Add

            shortcut = x
            x = ffn_block[0](x)
            x = ffn_block[1](x)
            x = ffn_block[2](x)
            x = ffn_block[3](x, training=training)
            x = ffn_block[4](x)
            x = ffn_block[5](x, training=training)
            x = ffn_block[6]([shortcut, x])

        x = self.final_norm(x)
        x = self.output_dense(x)
        return x
