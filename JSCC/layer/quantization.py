# import tensorflow as tf

# @tf.custom_gradient
# def QuantizationOp(x, B):
#     #step = tf.cast((2 ** B), dtype=tf.float32)
#     #result = tf.cast((tf.round(x * step - 0.5)), dtype=tf.float32)
#     #resultt = tf.py_function(func=Num2Bit, inp=[result, B], Tout=tf.float32)
#     #print("rrrrrrrrr" ,resultt)
    
#     step = tf.cast((2 ** B), dtype=tf.float32)
#     tempx = tf.cast((tf.floor(x * step)), dtype=tf.float32)
#     tempx = tf.reshape(tempx, (-1, tf.shape(tempx)[1], 1))
#     y_int = tempx
#     y = tf.math.floormod(y_int, 2)
#     for i in range(B):
#         y_mod = tf.math.floormod(y_int, 2)
#         y_int = tf.math.floordiv(y_int, 2)
#         y = tf.concat([y, y_mod], 2)

#     y = y[:, :, -1:0:-1]
#     y = y[:, :, (2 - B):]
#     result = tf.convert_to_tensor(tf.reshape(y, (-1, B * tf.shape(x)[1])), dtype=tf.float32)
#     #print (type(result))
#     #print('ttt',result,'ttt')
    
#     def custom_grad(dy):
#         grad = dy
#         return grad, None
    
#     return result, custom_grad


import tensorflow as tf

@tf.custom_gradient
def QuantizationOp(x, B):
    """
    把 x ∈ [0,1) 量化成 B bit 序列。
    - x : [batch , N] float32
    - B : int 或 0-D Tensor
    返回:
    - bits : [batch , N*B] float32 (高位在前，0/1)
    反向:
    - dL/dx  = dy   (STE)
    - dL/dB  = None (超参数不求导)
    """
    # -------- 前向 --------
    B = tf.cast(B, tf.int32)
    step = tf.pow(2.0, tf.cast(B, tf.float32))          # 2^B

    batch = tf.shape(x)[0]
    N     = tf.shape(x)[1]

    x_clip = tf.clip_by_value(x, 0.0, 1.0 - 1.0/step)   # 防溢出
    y_int  = tf.cast(tf.floor(x_clip * step), tf.int32) # [batch , N]

    # ------- 位展开：一次性用 bitwise 完成 -------
    # 先把整数扩展成 [..., N, 1]
    y_exp  = tf.expand_dims(y_int, -1)                  # [batch , N , 1]
    # 再与 [1,2,4,...] 做按位与，得到低位→高位序列
    masks  = tf.bitwise.left_shift(
        tf.ones((1,), tf.int32), tf.range(B, dtype=tf.int32)
    )                                                   # [B]  -> 1,2,4,...
    bits_lsb = tf.bitwise.bitwise_and(y_exp, masks)     # broadcast
    bits_lsb = tf.cast(bits_lsb > 0, tf.float32)        # 0/1

    # 翻转成 MSB→LSB 并 reshape
    bits = tf.reverse(bits_lsb, axis=[-1])              # [batch , N , B]
    bits = tf.reshape(bits, (batch, N * B))             # [batch , N*B]

    # -------- 自定义梯度 (STE) --------
    def grad(dy):
        return dy, dy        # dL/dx=dy,   dL/dB=None
    
    return bits, grad