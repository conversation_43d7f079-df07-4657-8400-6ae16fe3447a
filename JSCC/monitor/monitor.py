import os
import json
import numpy as np
import tensorflow as tf
from sionna.phy.utils import compute_ber,compute_bler
from pathlib import Path
import pickle
from loss.loss import Distortion

# 加载测试集b
# b = np.load('/home/<USER>/CDL_JSCC/CDL/dataset/AI-CSI-PV.npy')
test_data = np.load('/home/<USER>/CSI_xiaomi/JSCC/dataset/test_set.npy')

class TrainingMonitor:
    def __init__(self,
                 model,
                 optimizer,
                 test_info,  # [batch_size, test_snr_range]
                 save_dir="checkpoints",
                 test_interval=50,
                 metric='bler',
                 mode='min',
                 lr_decay=False,        # 新增：是否启用学习率衰减
                 initial_lr=None,       # 新增：初始学习率
                 min_lr=1e-6,           # 新增：最小学习率
                 decay_rate=0.96,       # 新增：衰减率
                 decay_steps=1000):     # 新增：衰减步长
        
        self.model = model
        self.optimizer = optimizer
        self.test_info = test_info
        self.save_dir = save_dir
        self.test_interval = test_interval
        self.metric = metric
        self.mode = mode
        
        # 新增的学习率衰减参数
        self.lr_decay = lr_decay
        self.initial_lr = initial_lr if initial_lr is not None else float(optimizer.learning_rate.numpy())
        self.min_lr = min_lr
        self.decay_rate = decay_rate
        self.decay_steps = decay_steps
        self.current_lr = self.initial_lr

        # 初始化目录结构
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # 训练状态
        self.iteration = 0
        self.best_metrics = []
        
        # 实时日志文件
        self.log_file = f'{self.save_dir}/training_log.txt'  # 修复了路径中的空格问题
        with open(self.log_file, 'w') as f:
            f.write("iteration\tloss\tlearning_rate\tmetrics\n")

    def _compute_metrics(self, b, b_hat):
        """统一计算所有通信指标"""
        distortion = Distortion()
        metrics = distortion(tf.convert_to_tensor(data), tf.convert_to_tensor(data_hat))
        return {
            'sgcs': float(metrics['sgcs'].numpy()),
            'mse': float(metrics['mse'].numpy()),
            'nmse': float(metrics['nmse'].numpy()),
        }


    def _test_model_large(self):
        """大规模测试，batch size固定为32，遍历整个test_data"""
        if not self.test_info:
            return None
        _, snr_range = self.test_info
        batch_size = 32
        num_samples = test_data.shape[0]
        num_batches = int(np.ceil(num_samples / batch_size))
        metrics = {'mmse': [], 'nmse': [], 'sgcs': []}
        for snr in snr_range:
            for i in range(num_batches):
                b_batch = test_data[i*batch_size : (i+1)*batch_size]
                b_hat = self._test_model_single_snr(b_batch, snr)
                b_hat = tf.cast(b_hat > 0, dtype=b_batch.dtype)
                m = self._compute_metrics(b_batch, b_hat)
                for k in metrics:
                    metrics[k].append(m[k])
        return {k: float(np.mean(v)) for k, v in metrics.items()}

    @tf.function(jit_compile=True)
    def _test_model_single_snr(self, b_batch, snr):
        """测试单个信噪比，输入为一个batch的b"""
        b_hat = self.model(b_batch.shape[0], snr, b_batch)
        return b_hat

    @tf.function(jit_compile=True)
    def _test_model_small(self):
        """小规模测试"""
        if not self.test_info:
            return None

        batch_size, snr_range = self.test_info
        repeats = batch_size // len(snr_range)
        remainder = batch_size % len(snr_range)
        snr_samples = np.concatenate([
            np.repeat(snr_range, repeats),
            np.random.choice(snr_range, remainder)
        ])
        np.random.shuffle(snr_samples)
        b, b_hat = self.model(batch_size, snr_samples)
        b_hat = tf.cast(b_hat > 0.5, dtype=b.dtype)
        return self._compute_metrics(b, b_hat)

    def _update_learning_rate(self):
        """指数衰减学习率更新"""
        if self.lr_decay:
            # 计算衰减后的学习率
            decay_factor = self.decay_rate ** (self.iteration / self.decay_steps)
            new_lr = max(self.initial_lr * decay_factor, self.min_lr)
            
            # 更新学习率
            if new_lr != self.current_lr:
                self.current_lr = new_lr
                self.optimizer.learning_rate.assign(new_lr)
                return True
        return False

    def on_iteration_end(self, train_loss, lr=None):
        """训练监控逻辑"""
        self.iteration += 1
        
        # 更新学习率（如果启用衰减）
        lr_updated = self._update_learning_rate()
        current_lr = float(lr) if lr is not None else float(self.optimizer.learning_rate.numpy())
        
        # 实时写入日志
        log_entry = {
            'iteration': self.iteration,
            'loss': float(train_loss),
            'lr': current_lr,
            'metrics': None
        }

        # 定期测试
        if self.test_interval > 0 and self.iteration % self.test_interval == 0:
            test_results = self._test_model_large()
            if test_results:
                current_metric = test_results[self.metric]
                log_entry['metrics'] = test_results
                
                # 更新最佳模型
                if (not self.best_metrics or 
                    (self.mode == 'min' and current_metric < self.best_metrics[-1][0]) or 
                    (self.mode == 'max' and current_metric > self.best_metrics[-1][0])):
                    
                    self.model.save_weights(self.save_dir, "best")
                    self.best_metrics.append((current_metric, self.iteration))
                    
                    # 打印当前指标
                    print(f"\nIteration {self.iteration}:")
                    for k, v in test_results.items():
                        print(f"{k}: {v:.4f}")
                    if lr_updated:
                        print(f"Learning rate updated to: {current_lr:.2e}")
        
        # 写入日志文件
        with open(self.log_file, 'a') as f:
            f.write(f"{log_entry['iteration']}\t{log_entry['loss']:.6f}\t{log_entry['lr']:.6f}\t")
            if log_entry['metrics']:
                f.write(json.dumps({k: f"{v:.4f}" for k, v in log_entry['metrics'].items()}))
            f.write("\n")