import tensorflow as tf
from tensorflow import keras
from layer.JSCC import <PERSON><PERSON><PERSON>_Encoder, <PERSON><PERSON><PERSON>_Decoder
from channel.channel import CDL_Layer,AWGN_Layer
from sionna.phy import Block
from layer.quantization import QuantizationOp
from layer.num2bit import Num2Bit
import os
import pickle

class JSCC_Model(Block):
    def __init__(self, config):
        super(JSCC_Model, self).__init__()
        # Encoder
        self.encoder = JSCC_Encoder(
            NUM_QUAN_DIM = config.quantization_dim, 
            NUM_FEEDBACK_BITS = config.feedback_bits, 
            NUM_QUAN_BITS = config.quantization_bits,
            EMBEDDING_DIM = config.embedding_dim, 
            NUM_HEAD = config.TF_heads, 
            CYCLE_NUM_enc = config.enc_TF_layers
        )

        # Decoder
        self.decoder = JSCC_Decoder(
            NUM_QUAN_DIM = config.quantization_dim, 
            NUM_FEEDBACK_BITS = config.feedback_bits, 
            NUM_QUAN_BITS = config.quantization_bits,
            EMBEDDING_DIM = config.embedding_dim, 
            NUM_HEAD = config.TF_heads, 
            CYCLE_NUM_dec = config.dec_TF_layers
        )
        self.quantization_bits=config.quantization_bits
        # Channel processing
        if config.channel_type == "CDL":
            self.channel = CDL_Layer(config)
        elif config.channel_type == "AWGN":
            self.channel = AWGN_Layer(config)
            
    def call(self, inputs, snr_dB=10, training=False):
        # Input shape: (batch_size, H, W*2)
        snr_linear = 10 ** (snr_dB / 10)
        no = 1 / snr_linear
        x = self.encoder(inputs,training=training)
        # print("encoder output shape:",x.shape)
        # x = QuantizationOp(x, self.quantization_bits)
        # print("quantize output shape:",x.shape)
        x = self.channel(x, no, tf.cast(self.quantization_bits, tf.int32))
        # print("channel output shape:",x.shape)
        x = self.decoder(x, training=training)
        # print("decoder output shape:",x.shape)
        x = tf.transpose(x, [0, 2, 1])
        x = tf.reshape(x, tf.shape(inputs)) 
        # print("reshape output shape:",x.shape)
        return x

    def save_weights(self, save_log, postfix):
        os.makedirs(save_log, exist_ok=True)

        # 保存 Encoder 和 Decoder 权重
        encoder_weights = self.encoder.get_weights()
        decoder_weights = self.decoder.get_weights()
        with open(f"{save_log}/JSCC_Encoder_{postfix}.pkl", 'wb') as f1:
            pickle.dump(encoder_weights, f1)
        with open(f"{save_log}/JSCC_Decoder_{postfix}.pkl", 'wb') as f2:
            pickle.dump(decoder_weights, f2)


    def load_weights(self, save_log, postfix):
        # 加载权重
        with open(f"{save_log}/JSCC_Encoder_{postfix}.pkl", 'rb') as f1:
            encoder_weights = pickle.load(f1)
        self.encoder.set_weights(encoder_weights)

        with open(f"{save_log}/JSCC_Decoder_{postfix}.pkl", 'rb') as f2:
            decoder_weights = pickle.load(f2)
        self.decoder.set_weights(decoder_weights)

