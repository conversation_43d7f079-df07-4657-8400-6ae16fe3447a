import tensorflow as tf

class SGCS(tf.keras.layers.Layer):
    def __init__(self, reduction='mean'):
        super(SGCS, self).__init__()
        self.reduction = reduction

    def call(self, X, Y):
        # 使用tf.keras.losses.CosineSimilarity，注意其返回的是负的余弦相似度
        X_flat = tf.reshape(X, [tf.shape(X)[0], -1])
        Y_flat = tf.reshape(Y, [tf.shape(Y)[0], -1])
        cos_sim = tf.keras.losses.CosineSimilarity(axis=1, reduction=tf.keras.losses.Reduction.NONE)(X_flat, Y_flat)
        # 由于CosineSimilarity返回负值，1+负值即为正的相似度
        return cos_sim if self.reduction == 'none' else tf.reduce_mean(cos_sim)


class MSE(tf.keras.layers.Layer):
    def __init__(self, reduction='mean'):
        super(MSE, self).__init__()
        self.reduction = reduction
        self.mse_fn = tf.keras.losses.MeanSquaredError(reduction=tf.keras.losses.Reduction.NONE)

    def call(self, X, Y):
        # 先展平每个样本，保持与原实现一致
        X_flat = tf.reshape(X, [tf.shape(X)[0], -1])
        Y_flat = tf.reshape(Y, [tf.shape(Y)[0], -1])
        mse_per_sample = self.mse_fn(X_flat, Y_flat)  # shape: [B]
        return mse_per_sample if self.reduction == 'none' else tf.reduce_mean(mse_per_sample)



class NMSE(tf.keras.layers.Layer):
    def __init__(self, reduction='mean'):
        super(NMSE, self).__init__()
        self.reduction = reduction
        self.mse = MSE(reduction='none')

    def call(self, X, Y):
        # 直接展平后用MSE计算误差
        X_flat = tf.reshape(X, [tf.shape(X)[0], -1])
        Y_flat = tf.reshape(Y, [tf.shape(Y)[0], -1])
        mse_per_sample = self.mse(X_flat, Y_flat)  # shape: [B]
        # 信号能量也用展平后的X计算
        power = tf.reduce_sum(tf.square(X_flat), axis=1)  # shape: [B]
        nmse = mse_per_sample / (power + 1e-8)
        return nmse if self.reduction == 'none' else tf.reduce_mean(nmse)



class Distortion(tf.keras.layers.Layer):
    def __init__(self, reduction='mean'):
        super(Distortion, self).__init__()
        self.sgcs = SGCS(reduction)
        self.mse = MSE(reduction)
        self.nmse = NMSE(reduction)

    def call(self, X, Y):
        return {
            'sgcs': self.sgcs(X, Y),   # 越大越好
            'mse': self.mse(X, Y),     # 越小越好
            'nmse': self.nmse(X, Y)    # 越小越好
        }
